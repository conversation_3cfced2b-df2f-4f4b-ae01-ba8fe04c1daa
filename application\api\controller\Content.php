<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;

/**
 * 公告
 * @Authod Jw
 * @Time 2021/6/15
 * @package app\api\controller
 */
class Content extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * @Method 公告列表
     * @Authod Jw
     * @Time 2021/6/21
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function list()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();
            $where = [];

            if ($params) {
                $where['type'] = $params['type'] ?? 1;
            }

            $data = Db::name('notice')
                ->where('status', 1)
                ->order('sort desc, id desc')
                ->where($where)
                ->field('id, content')
                ->select();

            $this->success('成功获取', $data);
        } else {
            $this->error('请求方式不正确');
        }
    }
}

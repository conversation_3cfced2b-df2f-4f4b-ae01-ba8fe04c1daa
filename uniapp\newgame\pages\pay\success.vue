<template>
  <view class="success-container">
    <view class="success-content">
      <view class="success-icon">
        <image src="/static/success-icon.png" mode="aspectFit" />
      </view>
      <view class="success-title">支付成功</view>
      <view class="success-desc">您的充值已到账</view>
      
      <view class="order-info">
        <view class="order-item">
          <text class="order-label">订单号：</text>
          <text class="order-value">{{ orderNo }}</text>
        </view>
        <view class="order-item">
          <text class="order-label">支付金额：</text>
          <text class="order-value">￥{{ amount }}</text>
        </view>
        <view class="order-item">
          <text class="order-label">充值金币：</text>
          <text class="order-value">{{ coins }}</text>
        </view>
      </view>
      
      <view class="button-group">
        <button class="primary-btn" @tap="goHome">返回首页</button>
        <button class="secondary-btn" @tap="goGame">去游戏</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      orderNo: '',
      amount: 0,
      coins: 0
    }
  },
  onLoad(options) {
    this.orderNo = options.order_no || '';
    this.amount = options.amount || 0;
    this.coins = options.coins || 0;
  },
  methods: {
    goHome() {
      uni.switchTab({
        url: '/pages/index/index'
      });
    },
    goGame() {
      uni.switchTab({
        url: '/pages/game/index'
      });
    }
  }
}
</script>

<style scoped>
.success-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.success-content {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
  width: 100%;
  max-width: 600rpx;
}

.success-icon {
  margin-bottom: 40rpx;
}

.success-icon image {
  width: 120rpx;
  height: 120rpx;
}

.success-title {
  font-size: 48rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.success-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
}

.order-info {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 60rpx;
}

.order-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.order-item:last-child {
  margin-bottom: 0;
}

.order-label {
  color: #666;
  font-size: 28rpx;
}

.order-value {
  color: #333;
  font-size: 28rpx;
  font-weight: bold;
}

.button-group {
  display: flex;
  gap: 20rpx;
}

.primary-btn, .secondary-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.primary-btn {
  background: linear-gradient(90deg, #3bb6ff, #1a5cff);
  color: white;
  border: none;
}

.secondary-btn {
  background: white;
  color: #1a5cff;
  border: 2rpx solid #1a5cff;
}
</style> 
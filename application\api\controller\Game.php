<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Cache;
use think\Db;

/**
 * 游戏
 * @Authod Jw
 * @Time 2021/6/15
 * @package app\api\controller
 */
class Game extends Api
{
    protected $noNeedLogin = ['category','list','delete_log','get_game_sn','test'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * @Method 分类列表
     * @Authod Jw
     * @Time 2021/6/21
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function category()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();

            $data = Db::name('game_category')->select();


            $this->success('成功获取',$data);
        }else{
            $this->error('请求方式不正确');
        }
    }

    /**
     * @Method 游戏列表
     * @Authod Jw
     * @Time 2021/6/21
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function list()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();
            $where = [];

            if ($params) {
                if (isset($params['c_id'])) {
                    $where['c_id'] = $params['c_id'];
                }
            }
            $where['is_delete'] = 0;

            $data = Db::name('game')->where($where)->where('status','>',0)->order('sort desc')->select();
            foreach ($data as $index => &$v) {
                $v['img'] = \config('site.domain').$v['img'];

                $v['player'] = Db::name('game_seat')
                    ->alias('s')
                    ->join(['fa_game_log l'],'s.id=l.seat_id and l.status in (0,1)','left')
                    ->join(['fa_user u'],'l.user_id=u.id','left')
                    ->where('s.game_id',$v['id'])
                    ->field('u.avatar,l.number')
                    ->select();
                foreach ($v['player'] as $ko => &$vo) {
                    $vo['avatar'] = \config('site.domain').$vo['avatar'];
                }
            }

            $this->success('成功获取',$data);
        }else{
            $this->error('请求方式不正确');
        }
    }

    /**
     * @Method 游戏详情
     * @Authod Jw
     * @Time 2021/6/21
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function detail()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();
            $where = [];

            if (!isset($params['gameId']) || empty($params['gameId'])) {
                $this->error('游戏Id不能为空');
            }

            $where['id'] = $params['gameId'];
            $where['is_delete'] = 0;
            $game = Db::name('game')
                ->where($where)
                ->where('status','>',0)
                ->field('id,sn,name,coin,status,players,playermode,heiqiplayer_signaling_url,heiqiplayer_sender_id,description,is_landscape,control_type,control_config,video_rotation')
                ->find();

            if (empty($game)) {
                $this->error('未查找到游戏');
            }
            if ($game['status'] == 2) {
                $this->error('游戏维护中');
            }

            //游戏座位数据
            $game['seats'] = Db::name('game_seat')
                ->alias('s')
                ->join(['fa_game_log l'],'s.id=l.seat_id and l.status in (0,1)','left')
                ->join(['fa_user u'],'u.id=l.user_id','left')
                ->where('s.game_id',$game['id'])
                ->field('s.id,s.game_id,s.number,s.status,u.nickname,u.avatar')
                ->select();
            foreach ($game['seats'] as &$v) {
                $v['avatar'] = config('site.domain').$v['avatar'];
            }
            unset($v);
            
            //游戏中用户
            $game['players'] = Db::name('game_log')
                ->alias('l')
                ->join(['fa_user u'],'l.user_id=u.id','left')
                ->where(['l.game_id'=>$game['id'],'l.status'=>1])
                ->field('l.seat_id,u.nickname,avatar')
                ->select();

            foreach ($game['players'] as $k => &$v) {
                $v['avatar'] = \config('site.domain').$v['avatar'];
            }
            unset($v);

            $game['userInfo'] = Db::name('user')->where('id',$this->auth->id)->field('id,nickname,avatar,money,score')->find();
            $game['userInfo']['avatar'] = $game['userInfo'] ? \config('site.domain').$game['userInfo']['avatar'] : '';


            //判断座位有没有满,0=当前空闲，1=座位已满，2=当前用户正在玩
            $game['is_full'] = 0;

            foreach ($game['seats'] as $k => $v) {//循环座位表，查看状态
                if ($v['status'] == 1) {//进行中
                    $game['is_full'] = 1;
                } else {
                    $game['is_full'] = 0;
                    break;
                }
            }

            //查询当前用户有没有在玩
            $game_log = Db::name('game_log')
                ->where(['game_id'=>$game['id'],'user_id'=>$game['userInfo']['id']])
                ->where('status','in',[0,1])
                ->field('id,status,number')
                ->find();
            $game['gameLogId'] = null;
            if ($game_log) {
                $game['is_full'] = 2;
                $game['gameLogId'] = $game_log['id'];
                $game['number'] = $game_log['number'];
                if ($game_log['status'] == 0) {//0=开始游戏等待响应中
                    $game['is_full'] = -1;//等待响应中...
                }
            }

            //平台客服二维码
            $game['service_qr'] = config('site.domain').config('site.service_qr');

            $this->success('成功获取',$game);
        }else{
            $this->error('请求方式不正确');
        }
    }

    /**
     * 清除未开始的游戏 和 修改设备在线离线状态
     * @Authod Jw
     * @Time 2025/5/16
     * @return string
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * @throws \think\exception\PDOException
     */
    public function delete_log()
    {
        $oneMinuteAgo = time() - 15;

        // 链式查询
        $result = Db::name('game_log')
            ->where('status', 0)                     // 状态为0
            ->where('createtime', '<=', $oneMinuteAgo) // 创建时间超过1分钟前
            ->whereTime('createtime', '>=', '-2 days')  // 获取两天内的数据
            ->select();

        foreach ($result as $index => $v) {
            Db::name('game_log')
                ->where('id',$v['id'])
                ->update(['status'=>-1,'updatetime'=>time()]);

            Db::name('game_seat')
                ->where(['id'=>$v['seat_id'],'status'=>1])
                ->update(['status'=>0,'updatetime'=>time()]);

        }

        $games = Db::name('game')->field('sn')->select();

        foreach ($games as $game) {
            $cacheKey = "device_online:{$game['sn']}";
            $cache = Cache::get($cacheKey);
            if (empty($cache)) {// 缓存不存在 -> 离线状态
                Db::name('game')
                    ->where('sn', $game['sn'])
                    ->update([
                        'device_status' => 0,
                        'offline_time' => date('Y-m-d H:i:s',time())
                    ]);
            }
        }

        return 'ok';
    }

    /**
     * 获取到游戏的SN号
     * <AUTHOR>
     * @date 2025-6-19
     * @return
     */
    public function get_game_sn()
    {
        $game_model = new \app\admin\model\Game();
        $game_model->get_game_sn();
        return 'ok';
    }

    /**
     * 获取配置信息
     * <AUTHOR>
     * @date 2025-8-6
     * @return
     */
    public function get_config()
    {
        $data['exchange'] = config('site.exchange');
        $this->success('成功获取',$data);
    }

    public function test(){
        $test = Cache::get('game_test');
        if (empty($test)) {
            print_r(12);
        }
        Cache::set('game_test',1,30);
        print_r(2);die;
    }

}

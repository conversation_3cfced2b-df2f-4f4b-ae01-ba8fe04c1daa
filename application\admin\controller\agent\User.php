<?php

namespace app\admin\controller\agent;

use app\common\controller\Backend;
use think\Db;

/**
 * 推广用户管理
 *
 * @icon fa fa-user
 */
class User extends Backend
{
    protected $searchFields = '';

    public function _initialize()
    {
        $this->noNeedRight = array('select_list');

        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];

            $admin = get_admin_info();
            $is_show = 1;
            if ($admin['group_id'] == get_agent_group_id()) {//组别6=代理
                //代理只查看自己的
                $where_arr['p_id'] = $admin['admin_id'];
                $is_show = 0;
            }

            if ($search_arr) {

            }

            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('user')
                ->where($where)
                ->where($where_arr)
                ->where('p_id','>',0)
                ->count();

            $list = Db::name('user')
                ->where($where)
                ->where($where_arr)
                ->where('agent_id', null)  // 使用链式where添加条件
                ->where('merchant_id', null)  // 使用链式where添加条件
                ->where('p_id','>',0)
                ->order('id', $order)
                ->field('password',true)
                ->limit($offset, $limit)
                ->select();

            foreach ($list as $index => &$v) {
                $v['is_show'] = $is_show;
                $agent = Db::name('admin')->where('id', $v['p_id'])->find();

                $v['agent_name'] = $agent['nickname'] ?? '';
                $v['agent_mobile'] = $agent['mobile'] ?? '';
            }

            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }


}
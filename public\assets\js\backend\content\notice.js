define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'content/notice/index' + location.search,
                    add_url: 'content/notice/add',
                    edit_url: 'content/notice/edit',
                    del_url: 'content/notice/del',
                    table: 'notice',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                escape: false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID'), operate: false},
                        {field: 'type',title: __('类型'),searchList: {"1":__('公告'),"2":__('通知'),"3":__('提醒')},formatter: function (value, row, index) {
                                if (row.type == 1) {
                                    return '公告';
                                }else if (row.type == 2) {
                                    return '通知';
                                }else{
                                    return '提醒';
                                }
                            }},
                        {field: 'title', title: __('标题'), operate: false},
                        {field: 'content', title: __('内容'), operate: false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,

                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        reset_password: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
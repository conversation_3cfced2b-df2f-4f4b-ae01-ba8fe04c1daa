<?php

namespace app\common\model;

use think\Log;
use think\Model;
use think\facade\Db;
use app\common\exception\PaymentException;
use addons\epay\library\Service;

class Payment extends Model
{
    // 支付状态常量
    const STATUS_PENDING  = 0; // 待支付
    const STATUS_PAID     = 1; // 已支付
    const STATUS_CLOSED   = 2; // 已关闭
    const STATUS_REFUNDED = 3; // 已退款

    /**
     * 统一下单支付
     * @param array $order 订单数据(必须包含order_no,amount,title)
     * @param string $type 支付类型 wechat/alipay
     * @param array $extend 扩展参数
     * @return array
     */
    public function pay($params)
    {
        // 参数校验增强
        $this->validateParams($params);

        // 构建标准化支付参数
        $payParams = $this->buildPayParams($params);

        try {
            // 检查是否为模拟支付模式
            if ($this->isMockPaymentMode()) {
                return $this->processMockPayment($payParams);
            }

            $result = Service::submitOrder($payParams);
            Log::record('支付请求参数：'.json_encode($payParams).' 响应：'.print_r($result, true));

            // 统一响应处理
            return $this->processPaymentResult($result);
        } catch (\Exception $e) {
            Log::error("支付异常 [{$e->getCode()}] {$e->getFile()}:{$e->getLine()} - {$e->getMessage()}");
            throw new \RuntimeException('支付系统繁忙，请稍后再试');
        }
    }

    /**
     * 检查是否为模拟支付模式
     */
    private function isMockPaymentMode()
    {
        // 可以通过配置文件或环境变量控制
        return true;
//        return config('payment.mock_mode', false) || env('PAYMENT_MOCK_MODE', false);
    }

    /**
     * 处理模拟支付
     */
    private function processMockPayment($payParams)
    {
        Log::info('模拟支付模式，订单号：' . $payParams['orderid']);
        
        // 根据支付类型和方式返回不同的模拟数据
        $type = $payParams['type'];
        $method = $payParams['method'];
        
        if ($type === 'wechat') {
            return $this->mockWechatPayment($method, $payParams);
        } else {
            return $this->mockAlipayPayment($method, $payParams);
        }
    }

    /**
     * 模拟微信支付
     */
    private function mockWechatPayment($method, $payParams)
    {
        switch ($method) {
            case 'app':
                // APP支付返回支付参数
                return [
                    'code' => 1,
                    'data' => [
                        'payment_type' => 'wechat',
                        'pay_params' => [
                            'appid' => 'mock_wx_appid',
                            'partnerid' => 'mock_partner_id',
                            'prepayid' => 'mock_prepay_id_' . time(),
                            'package' => 'Sign=WXPay',
                            'noncestr' => md5(uniqid()),
                            'timestamp' => (string)time(),
                            'sign' => md5('mock_sign')
                        ]
                    ]
                ];
            case 'wap':
            case 'web':
                // H5支付返回跳转链接
                return [
                    'code' => 1,
                    'data' => [
                        'payment_type' => 'wechat',
                        'redirect_url' => config('site.domain') . '/mock_payment/wechat?order_no=' . $payParams['orderid']
                    ]
                ];
            default:
                return [
                    'code' => 1,
                    'data' => [
                        'payment_type' => 'wechat',
                        'pay_url' => config('site.domain') . '/mock_payment/wechat?order_no=' . $payParams['orderid']
                    ]
                ];
        }
    }

    /**
     * 模拟支付宝支付
     */
    private function mockAlipayPayment($method, $payParams)
    {
        switch ($method) {
            case 'app':
                // APP支付返回支付参数
                return [
                    'code' => 1,
                    'data' => [
                        'payment_type' => 'alipay',
                        'pay_params' => 'mock_alipay_order_string_' . time()
                    ]
                ];
            case 'wap':
            case 'web':
                // H5支付返回跳转链接
                return [
                    'code' => 1,
                    'data' => [
                        'payment_type' => 'alipay',
                        'redirect_url' => config('site.domain') . '/mock_payment/alipay?order_no=' . $payParams['orderid']
                    ]
                ];
            default:
                return [
                    'code' => 1,
                    'data' => [
                        'payment_type' => 'alipay',
                        'pay_url' => config('site.domain') . '/mock_payment/alipay?order_no=' . $payParams['orderid']
                    ]
                ];
        }
    }

    // 新增私有方法
    private function validateParams(&$params)
    {
        $required = [
            'amount'    => ['type' => 'numeric', 'min' => 0.01, 'msg' => '支付金额必须大于0'],
            'order_no'  => ['type' => 'string', 'msg' => '订单号不能为空'],
            'type'      => ['type' => 'in:wechat,alipay', 'msg' => '不支持的支付类型'],
            'method'    => ['type' => 'in:web,wap,miniapp,mp,app', 'msg' => '不支持的支付方式']
        ];

        foreach ($required as $key => $rule) {
            if (empty($params[$key])) {
                throw new \InvalidArgumentException("{$key} 参数缺失");
            }

            switch ($rule['type']) {
                case 'numeric':
                    $params[$key] = (float)$params[$key];
                    if ($params[$key] < $rule['min']) {
                        throw new \InvalidArgumentException($rule['msg']);
                    }
                    break;
                case 'in':
                    if (!in_array($params[$key], explode(':', $rule['type']))) {
                        throw new \InvalidArgumentException($rule['msg']);
                    }
                    break;
            }
        }

        // 微信特殊校验
        if ($params['type'] === 'wechat' && in_array($params['method'], ['miniapp', 'mp']) && empty($params['openid'])) {
            throw new \InvalidArgumentException('微信支付需要openid参数');
        }
    }

    private function buildPayParams($params)
    {
        return [
            'type'      => $params['type'],
            'orderid'   => $params['order_no'],
            'title'     => $params['title'] ?? '虚拟商品充值',
            'amount'    => (float)$params['amount'],
            'method'    => $params['method'],
            'notifyurl' => config('site.domain')."/payment/notify/{$params['type']}",
            'openid'    => $params['openid'] ?? null,
            'parameter' => ['order_no' => $params['order_no']] // 透传订单号
        ];
    }

    private function processPaymentResult($result)
    {
        // 处理对象响应
        if ($result instanceof \addons\epay\library\RedirectResponse) {
            return [
                'code' => 1,
                'data' => [
                    'payment_type' => 'wechat',
                    'redirect_url' => $result->getTargetUrl()
                ]
            ];
        }

        // 转换为数组处理
        $response = json_decode(json_encode($result), true);

        // 统一获取跳转URL
        $redirectUrl = $this->extractRedirectUrl($response ?? $result);

        if ($redirectUrl) {
            return [
                'code' => 1,
                'data' => [
                    'payment_type' => 'wechat',
                    'redirect_url' => $redirectUrl
                ]
            ];
        }

        // 处理原生支付参数（如APP支付）
        if (isset($response['prepay_id'])) {
            return [
                'code' => 1,
                'data' => [
                    'payment_type' => 'wechat',
                    'pay_params'   => $this->buildAppPayParams($response)
                ]
            ];
        }

        throw new \RuntimeException('无法解析支付接口响应');
    }

    private function extractRedirectUrl($response)
    {
        // 优先从数组获取
        if (is_array($response)) {
            return $response['redirect_url']
                ?? $response['url']
                ?? $response['h5_url']
                ?? null;
        }

        // HTML页面跳转解析
        if (is_string($response)) {
            preg_match('/<meta[^>]+url=(.*?)(?=["\'])/i', $response, $matches);
            return $matches[1] ?? null;
        }

        return null;
    }

    private function buildAppPayParams($response)
    {
        // 示例：构建APP支付参数
        $params = [
            'appid'     => config('wechat.app_id'),
            'partnerid' => config('wechat.mch_id'),
            'prepayid'  => $response['prepay_id'],
            'package'   => 'Sign=WXPay',
            'noncestr'  => md5(uniqid()),
            'timestamp' => (string)time(),
        ];
        
        // 生成签名
        $params['sign'] = $this->generateSign($params);
        
        return $params;
    }

    private function generateSign($params)
    {
        // 示例：生成签名
        // 按字典序排序
        ksort($params);
        
        // 构建签名字符串
        $stringA = '';
        foreach ($params as $key => $value) {
            $stringA .= $key . '=' . $value . '&';
        }
        $stringA .= 'key=' . config('wechat.key');
        
        // 返回MD5签名
        return strtoupper(md5($stringA));
    }

    /**
     * 统一退款
     * @param array $order 订单数据(必须包含order_no,pay_amount,refund_amount)
     * @param string $payType 原支付方式 wechat/alipay
     * @return array
     */
    public function refund($order, $payType = 'wechat')
    {
        Db::startTrans();
        try {
            // 微信退款
            if ($payType == 'wechat') {
                $pay = new Pay();
                $result = $pay->refund([
                    'transaction_id' => $order['transaction_id'],
                    'out_refund_no'  => 'RF' . date('YmdHis') . rand(1000,9999),
                    'total_fee'      => $order['pay_amount'] * 100,
                    'refund_fee'     => $order['refund_amount'] * 100
                ]);
            }
            // 支付宝退款
            else {
                require_once(ROOT_PATH."/extend/aop/AopCertClient.php");
                // 这里需要根据支付宝SDK实现具体退款逻辑
                // 参考Svip模型中的alipay_refund方法实现
            }

            if ($result['code'] == 1) {
                Db::commit();
                return ['code' => 1, 'msg' => '退款成功'];
            }

            throw new \Exception($result['msg'] ?? '退款失败');
        } catch (\Exception $e) {
            Db::rollback();
            Log::error("退款失败：" . $e->getMessage());
            return ['code' => 0, 'msg' => '退款处理失败'];
        }
    }

    /**
     * 更新支付状态（原子操作）
     */
    public static function updatePaymentStatus($orderNo, $notifyData)
    {
        $payment = self::where('order_no', $orderNo)
            ->lock(true)
            ->find();

        if (!$payment) {
            throw new \Exception('支付订单不存在');
        }

        // 状态机校验
        if ($payment->status != self::STATUS_PENDING) {
            throw new \Exception('订单状态不可变更');
        }

        $payment->save([
            'status' => self::STATUS_PAID,
            'trade_no' => $notifyData['transaction_id'],
            'pay_time' => time(),
            'callback_data' => json_encode($notifyData)
        ]);

        return $payment;
    }

    /**
     * 更新退款状态（完整方法）
     * @param string $orderNo
     * @param float $refundAmount
     */
    public static function updateRefundStatus($orderNo, $refundAmount)
    {
        $payment = self::where('order_no', $orderNo)
            ->lock(true)
            ->find();

        if (!$payment) {
            throw new PaymentException('支付订单不存在');
        }

        // 状态校验
        if ($payment->status != self::STATUS_PAID) {
            throw new PaymentException('只有已支付订单可以退款');
        }

        // 退款金额校验
        if (bccomp($refundAmount, $payment->amount, 2) > 0) {
            throw new PaymentException('退款金额不能超过支付金额');
        }

        // 更新支付记录
        $payment->save([
            'status'       => self::STATUS_REFUNDED,
            'refund_amount' => Db::raw("refund_amount + {$refundAmount}"),
            'updatetime'    => time()
        ]);

        // 调用第三方退款接口（伪代码）
        self::callThirdPartyRefund($payment, $refundAmount);

        return $payment;
    }

    /**
     * 调用第三方退款（示例方法）
     */
    private static function callThirdPartyRefund($payment, $amount)
    {
        // 根据支付类型选择驱动
        $driver = self::getPaymentDriver($payment->pay_type);

        try {
            $result = $driver->refund([
                'trade_no'      => $payment->trade_no,
                'refund_amount' => $amount,
                'out_refund_no' => self::generateRefundNo()
            ]);

            if (!$result['success']) {
                throw new PaymentException('第三方退款失败：'.$result['msg']);
            }
        } catch (\Exception $e) {
            throw new PaymentException('退款接口调用异常：'.$e->getMessage());
        }
    }

    /**
     * 生成退款单号（示例）
     */
    private static function generateRefundNo()
    {
        return 'RF'.date('YmdHis').mt_rand(1000,9999);
    }

    /**
     * 获取支付驱动（示例）
     */
    private static function getPaymentDriver($payType)
    {
        $drivers = [
            'wechat' => \app\common\lib\payment\Wechat::class,
            'alipay' => \app\common\lib\payment\Alipay::class,
        ];

        if (!isset($drivers[$payType])) {
            throw new PaymentException('不支持的支付方式');
        }

        return new $drivers[$payType]();
    }
}
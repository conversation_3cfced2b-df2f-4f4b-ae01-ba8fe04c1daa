<?php

namespace app\admin\controller\content;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 游戏列表
 * @Authod JW
 * @Time 2023/02/02
 * @package app\admin\controller\agent
 */
class Notice extends Backend
{
    /**
     * Withdraw模型对象
     * @var \app\admin\model\Agent
     */
    protected $model = null;

    protected $noNeedRight = '';
    protected $multiFields = "deleted,rent_limit";
    public function _initialize()
    {
        $this->noNeedRight = array('select_list');

        parent::_initialize();
        $this->model = model('game');
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {

            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            //搜索提交过来的数据
            $search_arr = json_decode($this->request->request('filter'),1);
            $where_arr = [];


            $this->request->get(['filter'=>json_encode($search_arr)]);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $total = Db::name('notice')
                ->where($where)
                ->where($where_arr)
                ->order($sort, $order)
                ->count();

            $list = Db::name('notice')
                ->where($where)
                ->where($where_arr)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();

            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     * @Authod Jw
     * @Time 2023/2/16
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                $time = time();

                try {
                    $insert = [
                        'title'         => $params['title'],
                        'content'       => $params['content'],
                        'createtime'    => $time,
                        'updatetime'    => $time,
                        'status'        => $params['status'],
                        'type'          => $params['type'],
                        'sort'          => $params['sort'],
                    ];

                    Db::name('notice')->insert($insert);
                } catch (\Exception $e) {
                    $this->error($e->getMessage());
                }
                $this->success('成功');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * @Method 编辑
     * @Authod JW
     * @Time 2020/12/8
     * @param null $ids
     * @return string
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function edit($ids = null)
    {
        $row = Db::name('notice')->where('id',$ids)->find();

        if (!$row) {
            $this->error(__('No Results were found'));
        }

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                $time = time();
                $params['updatetime'] = $time;

                $result = Db::name('notice')->where('id',$ids)->update($params);
                if ($result) {
                    $this->success('成功');
                }
                $this->error('失败');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * @Method 删除
     * @Authod JW
     * @Time 2021/1/28
     * @param string $ids
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * @throws \think\exception\PDOException
     */
    public function del($ids = "")
    {
        if ($ids) {
            $id = Db::name('notice')->where(['id'=>$ids])->value('id');
            if (empty($id)) {
                $this->error('删除失败，不存在');
            }
            $result = Db::name('notice')->where('id',$ids)->delete();
            if ($result) {
                $this->success('成功');
            }
            $this->error('删除失败');
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }



}

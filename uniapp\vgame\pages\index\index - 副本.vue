<template>
	<view class="content">
		<!-- top轮播图 -->
		<!-- <swiper :autoplay="true" :interval="3000" :duration="500" circular indicator-dots indicator-active-color="#fff"
			style="height: 400rpx">

			<swiper-item v-for="(item, index) in swiperList" :key="index">
				<image :src="item" mode="aspectFill" style="width: 100%; height: 100%" />
			</swiper-item>
		</swiper> -->

		<!-- 固定定位的分类导航 -->
		<view class="sticky-nav">
			<scroll-view scroll-x class="nav-scroll" :scroll-left="scrollLeft" show-scrollbar>
				<view class="nav-container">
					<view v-for="(category,index) in categories" :key="index"
						:class="['nav-item', activeCategory === category.id ? 'active' : '']"
						@click="switchCategory(category.id)">
						{{category.name}}
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 游戏列表容器 -->
		<view class="game-list-container">
			<!-- 状态切换按钮 - 现在放在游戏列表容器内但绝对定位 -->
			<!--      <view class="filter-btn" @click="toggleFilter">
        <view class="filter-icon">
          <image 
            v-if="filterType === 'all'"
            src="/static/filter-icon.png" 
            class="filter-img"/>
          <image 
            v-else
            src="/static/idle-icon.png" 
            class="filter-img"/>
        </view>
        <view class="filter-text">
          {{filterType === 'all' ? '筛选' : '空闲'}}
        </view>
      </view> -->

			<!-- 游戏列表 -->
			<view class="game-list">
				<view v-for="(game,index) in filteredGames" :key="index" class="game-item"
					@click="handleGameClick(game)">

					<!-- 游戏图片 -->
					<view class="img-container">
						<image :src="game.img" mode="aspectFill" class="game-img" />
					</view>

					<!-- 左上角状态标签 -->
					<!-- <view class="corner-tag" v-if=" game.players > 0">
						<text class="tag-text">热玩中: {{game.players || 1}}人</text>
					</view> -->

					<!-- 状态标签 -->
					<view :class="['status-tag', 
                   game.status === 1 ? 'hot' : 
                   game.status === 0 ? 'free' : 'maintain']">
						{{ game.status === 1 ? '热玩中' : game.status === 0 ? '空闲' : '维护中' }}
					</view>

					<!-- 游戏信息 -->
					<view class="game-info">
						<text class="game-name">{{game.name}}</text>
						<view class="coin-box">
							<image src="/static/coin.png" class="coin-icon" />
							<text class="coin-num">{{game.coin}}</text>
						</view>
					</view>

					<!-- 右下角VIP标签 -->
					<view class="vip-tag" v-if="game.level > 0">
						V{{game.level}}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '@/utils/request'
	import auth from '@/utils/auth'
	import config from '@/config.js'
	export default {
		data() {
			return {
				swiperList: [
					'/static/banner/1.jpg',
					'/static/banner/2.jpg',
					'/static/banner/3.jpg',
					'/static/banner/4.jpg'
				],
				activeCategory: null, // 当前选中分类
				filterType: 'all', // 筛选类型：all/hot/idle
				categories: [], // 游戏分类数据
				games: [], // 当前分类所有游戏
				isLogin: false, // 登录状态（需根据实际登录逻辑处理
				tabs: ['首页', '充值', '活动', '任务'],
				scrollLeft: 0,
			}
		},
		onLoad() {

		},
		computed: {
			filteredGames() {
				return this.games.filter(game => {
					if (this.filterType === 'all') return true
					return game.status === this.filterType
				})
			}
		},
		onShow() {
			this.loadCategories()
		},
		methods: {
			// 获取分类数据
			async loadCategories() {
				try {
					const res = await request({
						url: '/api/game/category',
						method: 'POST',
					})

					this.categories = res.data
					if (this.categories.length > 0) {
						this.activeCategory = this.categories[0].id
						this.loadGames()
					}
				} catch (error) {
					console.error('获取分类失败', error)
				}
			},
			// 获取游戏数据
			async loadGames() {
				try {
					const res = await request({
						url: '/api/game/list',
						method: 'POST',
						data: {
							category: this.activeCategory
						}
					})
					this.games = res.data
				} catch (error) {
					console.error('获取游戏失败', error)
				}
			},
			// 处理游戏点击
			handleGameClick(game) {
				if (!auth.checkLogin()) { //未登录
					uni.navigateTo({
						url: '/pages/login/login'
					})
					return
				}
				if (game.status === 2) {
					uni.showToast({
						title: '该游戏正在维护中',
						icon: 'none',
						duration: 2000
					})
					return
				}
				const userInfo = uni.getStorageSync(config.userInfo) //用户信息

				// 检查VIP等级
				if (game.level > 0 && userInfo?.level < game.level) {
					uni.showModal({
						title: '等级不足',
						content: `需要会员等级LV${game.level}才能游玩`,
						showCancel: false,
						confirmText: '去升级',
						success: () => {
							// uni.navigateTo({ url: '/pages/vip/upgrade' })
						}
					})
					return
				}

				// if (game.is_landscape == 1) { //需要横屏
				// 	uni.navigateTo({
				// 		url: `/pages/game/game?gameId=${game.id}`
				// 	})
				// } else { //竖屏
				// 	uni.navigateTo({
				// 		url: `/pages/game/coin?gameId=${game.id}`
				// 	})
				// }

				uni.navigateTo({
					url: `/pages/game/game?gameId=${game.id}&is_landscape=${game.is_landscape || 1}`
				})
			},
			// 处理分类点击
			switchCategory(categoryId) {
				this.activeCategory = categoryId
				if (this.filterType === 0) {
					this.filterType = 'all'
				}
				this.loadGames()
			},
			// 切换筛选状态
			toggleFilter() {
				this.filterType = this.filterType === 'all' ? 0 : 'all'
			},
			switchTab(tabName) {
				// 路径必须去掉开头的斜杠
				const pageMap = {
					'首页': 'pages/index/index',
					'充值': 'pages/recharge/recharge',
					'活动': 'pages/activity/activity',
					'任务': 'pages/task/task', // 关键：路径格式为"pages/xxx/xxx"
					'我的': 'pages/my/my'
				};
				uni.switchTab({
					url: pageMap[tabName] // 正确格式的路径
				});
			}
		}
	}
</script>

<style scoped>
	.maintain { 
	  background: #999;
	  /* 可以添加更多维护状态样式 */
	}
	/* 分类导航 */
	.sticky-nav {
		position: sticky;
		top: 0;
		z-index: 999;
		background: #576ECE;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
		height: 100rpx;
	}

	.nav-scroll {
		width: 100%;
		height: 100%;
		white-space: nowrap;
	}

	.nav-container {
		display: inline-flex;
		height: 100%;
		min-width: 100%;
		justify-content: space-around;
	}

	.nav-item {
		flex: 1;
		min-width: 150rpx;
		max-width: 250rpx;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.7);
		transition: all 0.3s;
		position: relative;
	}

	.nav-item.active {
		color: #fff;
		font-weight: bold;
	}

	.nav-item.active::after {
		content: '';
		position: absolute;
		bottom: 10rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 60%;
		height: 4rpx;
		background: #fff;
		border-radius: 2rpx;
	}

	/* 游戏列表 */
	.game-list {
		display: flex;
		flex-wrap: wrap;
		padding: 20rpx;
		justify-content: space-between;

	}

	/* 游戏项 */
	.game-item {
		position: relative;
		width: 48%;
		height: 500rpx;
		margin-bottom: 20rpx;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
		display: flex;
		flex-direction: column;
	}

	/* 新增图片容器 */
	.img-container {
		flex: 1;
		/* 占据剩余空间 */
		position: relative;
		overflow: hidden;
	}


	/* 游戏背景图 */
	.game-bg {
		position: absolute;
		width: 100%;
		height: 100%;
		z-index: 1;
	}

	/* 左上角标签 */
	.corner-tag {
		position: absolute;
		top: 10rpx;
		left: 10rpx;
		z-index: 2;
		padding: 6rpx 16rpx;
		border-radius: 20rpx;
		background-color: rgba(0, 0, 0, 0);
		/* 半透明黑色背景 */
		backdrop-filter: blur(5px);
		/* 毛玻璃效果 */
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		/* 半透明边框 */
		font-size: 22rpx;
		color: white;
	}


	/* 状态标签 */
	.status-tag {
		position: absolute;
		top: 10rpx;
		right: 10rpx;
		z-index: 2;
		padding: 6rpx 20rpx;
		border-radius: 20rpx;
		font-size: 22rpx;
		color: white;
	}

	.game-name {
		font-size: 28rpx;
		color: white;
		font-weight: bold;
		text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.8);
		width: 100%;
		padding: 3rpx 50rpx;
		border-radius: 20rpx;
		background-color: rgba(0, 0, 0, 0);
		/* 半透明黑色背景 */
		backdrop-filter: blur(5px);
		/* 毛玻璃效果 */
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		/* 半透明边框 */
	}

	/* 金币显示 */
	.coin-box {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 3rpx 10rpx;
		margin-top: 10rpx;
		/* border-radius: 20rpx;
  background-color: rgba(0, 0, 0, 0);
  backdrop-filter: blur(5px);
  border: 1rpx solid rgba(255, 255, 255, 0.2); 
  */
	}


	.hot {
		background: #ff4545;
	}

	.free {
		background: #80ED89;
	}

	/* 游戏图片 */
	.game-img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	/* 游戏信息 */
	.game-info {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 16rpx;
		z-index: 2;
		flex-direction: column;
		align-items: center;
		/* 水平居中 */
		text-align: center;
		/* 文字居中 */

	}

	.coin-icon {
		width: 28rpx;
		height: 28rpx;
		filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.8));
	}

	.coin-num {
		color: #FFD700;
		font-weight: bold;
		font-size: 26rpx;
		margin-left: 6rpx;
		text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.8);
	}


	/* 游戏列表容器 */
	.game-list-container {
		position: relative;
		padding: 20rpx;
		background-color: #669CFE;
		/* 新增浅灰色背景 */
		min-height: 100vh;
		/* 确保背景覆盖整个可视区域 */
	}

	/* 筛选按钮 */
	.filter-btn {
		position: absolute;
		top: 20rpx;
		left: 20rpx;
		width: 120rpx;
		height: 120rpx;
		border-radius: 60rpx;
		background: #ffffff;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		overflow: hidden;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		z-index: 10;
		/* 确保按钮在游戏列表之上 */
	}

	/* 图标部分 (占3/4) */
	.filter-icon {
		height: 75%;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.filter-img {
		width: 60rpx;
		height: 60rpx;
	}

	/* 文字部分 (占1/4) */
	.filter-text {
		height: 25%;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 24rpx;
		color: #ffffff;
		background: #97694F;
	}

	/* 游戏列表 */
	.game-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		position: relative;
		/* 确保z-index生效 */
		z-index: 1;
		/* 低于按钮的z-index */
	}

	/* 添加VIP标签样式 */
	.vip-tag {
		position: absolute;
		bottom: 10rpx;
		right: 10rpx;
		z-index: 2;
		padding: 4rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		font-weight: bold;
		color: #fff;
		background: linear-gradient(45deg, #ffd700, #c5a000);
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
		border: 1rpx solid rgba(255, 215, 0, 0.3);
	}
</style>
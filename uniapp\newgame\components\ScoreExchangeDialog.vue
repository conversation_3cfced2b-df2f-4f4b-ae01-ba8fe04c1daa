<template>
	<view v-if="show" class="exchange-dialog-mask" @tap="close">
		<view class="exchange-dialog-content" @tap.stop>
			<view class="modal-header">
				<text class="modal-title">兑换金币</text>
				<image class="close-icon" src="/static/close.png" @tap="close"></image>
			</view>
			<view class="modal-body">
				<!-- 当前积分 -->
				<text class="section-label">当前积分</text>
				<view class="info-box">
					<image class="box-icon" src="/static/score.png" />
					<text class="box-value">{{ userScore }}</text>
				</view>

				<!-- 兑换 -->
				<view class="section-header">
					<text class="section-label">兑换</text>
					<view class="exchange-rate">
						<text class="rate-text">当前比例：</text>
						<text class="rate-score">{{ scoreToCoinRate.split(':')[0] }}</text>
						<text class="rate-text">:</text>
						<text class="rate-coin">{{ scoreToCoinRate.split(':')[1] }}</text>
					</view>
				</view>
				<view class="info-box">
					<image class="box-icon" src="/static/coin.png" />
					<image class="swap-icon" src="/static/swap.png" />
					<input class="box-input" type="number" v-model="inputAmount" placeholder="0" />
					<text class="box-unit">币</text>
				</view>

				<!-- 剩余积分 -->
				<text class="section-label">剩余积分</text>
				<view class="info-box">
					<image class="box-icon" src="/static/score.png" />
					<image class="swap-icon" src="/static/swap.png" />
					<text class="box-value" :style="{ color: remainingScoreColor }">{{ remainingScore }}</text>
				</view>

				<button class="submit-btn" @tap="submitExchange">立即兑换</button>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '@/utils/request.js';

	export default {
		name: 'ScoreExchangeDialog',

		props: {
			show: {
				type: Boolean,
				default: false
			},
			userInfo: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				exchangeConfig: {},
				inputAmount: null,
			};
		},
		computed: {
			userScore() {
				return this.userInfo.score || 0;
			},
			userVipLevel() {
				return this.userInfo.vip_level || 0;
			},
			// 根据输入的金币数量计算需要的积分
			scoreNeeded() {
				if (!this.inputAmount || this.inputAmount <= 0) return 0;
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey) return 0;
				const coins = this.exchangeConfig[rateKey];
				return Math.ceil((this.inputAmount / Number(coins)) * Number(rateKey));
			},
			// 最大可兑换金币数
			maxExchangeCoins() {
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey) return 0;
				const coins = this.exchangeConfig[rateKey];
				return Math.floor((this.userScore / Number(rateKey)) * Number(coins));
			},
			// 兑换比例显示
			scoreToCoinRate() {
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey) return 'N/A';
				const coins = this.exchangeConfig[rateKey];
				return `${rateKey}:${coins}`;
			},
			// 剩余积分计算
			remainingScore() {
				if (!this.inputAmount || this.inputAmount <= 0) return this.userScore;
				return this.userScore - this.scoreNeeded;
			},
			remainingScoreColor() {
				return this.remainingScore < 0 ? '#ff5555' : '#FFD700';
			}
		},
		watch: {
			async show(newVal) {
				if (newVal) {
					const success = await this.getConfig();
					if (success) {
						// 默认设置为最大可兑换的金币数量
						this.inputAmount = this.maxExchangeCoins;
					}
				}
			}
		},
		methods: {
			async getConfig() {
				try {
					const res = await request({
						url: '/api/game/get_config'
					});
					if (res.code === 1 && res.data.exchange) {
						this.exchangeConfig = res.data.exchange;
						return true;
					} else {
						this.showError('获取兑换配置失败');
						return false;
					}
				} catch (error) {
					this.showError('网络错误，请稍后重试');
					return false;
				}
			},
			close() {
				this.inputAmount = null;
				this.$emit('close');
			},
			async submitExchange() {
				if (!this.inputAmount || this.inputAmount <= 0) {
					this.showError('请输入有效的金币数量');
					return;
				}
				if (this.scoreNeeded > this.userScore) {
					this.showError('积分不足');
					return;
				}

				try {
					const res = await request({
						url: '/api/user/scoreExchange',
						method: 'POST',
						data: {
							amount: this.inputAmount,
							score_cost: this.scoreNeeded
						}
					});

					if (res.code === 1) {
						uni.showToast({
							title: '兑换成功',
							icon: 'success'
						});
						this.$emit('exchangeSuccess');
						this.close();
					} else {
						this.showError(res.msg || '兑换失败');
					}
				} catch (error) {
					this.showError('网络错误，请稍后重试');
				}
			},
			showError(msg) {
				uni.showToast({
					title: msg,
					icon: 'none'
				});
			}
		}
	};
</script>

<style scoped>
	.exchange-dialog-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.exchange-dialog-content {
		background-color: #301E46;
		border-radius: 20rpx;
		width: 650rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
	}

	.modal-header {
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		padding-top: 30rpx;
	}

	.modal-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #fff;
	}

	.close-icon {
		position: absolute;
		right: 30rpx;
		width: 50rpx;
		height: 50rpx;
	}

	.modal-body {
		padding: 40rpx;
	}

	/* 区域标签样式 */
	.section-label {
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 15rpx;
		display: block;
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
	}

	.exchange-rate {
		display: flex;
		align-items: center;
		font-size: 28rpx;
	}

	.rate-text {
		color: #fff;
	}

	.rate-score {
		color: #FFD700; /* 积分颜色 - 金色 */
	}

	.rate-coin {
		color: #FFA500; /* 金币颜色 - 橙色 */
	}

	/* 信息框样式 */
	.info-box {
		background-color: #3F3055;
		border-radius: 15rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		display: flex;
		align-items: center;
		position: relative;
	}

	.box-icon {
		width: 60rpx;
		height: 60rpx;
	}

	.swap-icon {
		width: 40rpx;
		height: 40rpx;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
	}

	.box-value {
		color: #FFD700; /* 积分颜色 - 金色 */
		font-size: 48rpx;
		font-weight: bold;
		flex: 1;
		text-align: right;
	}

	.box-input {
		color: #FFA500; /* 金币颜色 - 橙色 */
		font-size: 48rpx;
		font-weight: bold;
		flex: 1;
		text-align: right;
		background: transparent;
		border: none;
	}

	.box-unit {
		color: #fff;
		font-size: 36rpx;
		font-weight: bold;
		margin-left: 20rpx;
	}


	.submit-btn {
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;
		border-radius: 45rpx;
		font-size: 30rpx;
		color: #fff;
		background: url('/static/mine/button.png') no-repeat center/100% 100%;
		border: none;
		text-align: center;
		margin-top: 40rpx;
	}
</style>
<template>
	<view v-if="show" class="exchange-dialog-mask" @tap="close">
		<view class="exchange-dialog-content" @tap.stop>
			<view class="modal-header">
				<text class="modal-title">兑换金币</text>
				<image class="close-icon" src="/static/close.png" @tap="close"></image>
			</view>
			<view class="modal-body">
				<!-- 积分输入框 -->
				<view class="result-row">
					<image class="input-icon" src="/static/score.png" />
					<input class="result-input" type="number" v-model="inputAmount" placeholder="请输入兑换积分" />
				</view>

				<!-- 转换箭头 -->
				<view class="arrow-container">
					<image class="arrow-icon" src="/static/swap.png" />
				</view>

				<!-- 金币输出框 -->
				<view class="result-row">
					<image class="input-icon" src="/static/coin.png" />
					<input class="result-input" type="number" v-model="coinOutput" placeholder="0" disabled />
				</view>

				<!-- 当前积分显示 -->
				<view class="current-info">
					<text class="current-score">当前积分：{{ userScore }}</text>
					<text class="max-coins">可兑换金币：{{ maxExchangeCoins }}</text>
				</view>

				<!-- VIP信息和兑换比例 -->
				<view class="vip-info">
					<view class="vip-badge">
						<text>当前vip {{ userVipLevel }}</text>
					</view>
					<text class="exchange-rate">兑换比例：{{ scoreToCoinRate }}</text>
				</view>

				<button class="submit-btn" @tap="submitExchange">立即兑换</button>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '@/utils/request.js';

	export default {
		name: 'ScoreExchangeDialog',

		props: {
			show: {
				type: Boolean,
				default: false
			},
			userInfo: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				exchangeConfig: {},
				inputAmount: null,
			};
		},
		computed: {
			userScore() {
				return this.userInfo.score || 0;
			},
			userVipLevel() {
				return this.userInfo.vip_level || 0;
			},
			// 根据输入的积分计算可兑换的金币
			coinOutput() {
				if (!this.inputAmount || this.inputAmount <= 0) return 0;
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey) return 0;
				const coins = this.exchangeConfig[rateKey];
				return Math.floor((this.inputAmount / Number(rateKey)) * Number(coins));
			},
			// 最大可兑换金币数
			maxExchangeCoins() {
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey) return 0;
				const coins = this.exchangeConfig[rateKey];
				return Math.floor((this.userScore / Number(rateKey)) * Number(coins));
			},
			// 兑换比例显示
			scoreToCoinRate() {
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey) return 'N/A';
				const coins = this.exchangeConfig[rateKey];
				return `${rateKey}:${coins}`;
			},
			// 剩余积分计算
			remainingScore() {
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey || !this.inputAmount || this.inputAmount <= 0) return this.userScore;
				const coins = this.exchangeConfig[rateKey];
				const cost = Math.ceil((this.inputAmount / Number(coins)) * Number(rateKey));
				return this.userScore - cost;
			},
			remainingScoreColor() {
				return this.remainingScore < 0 ? '#ff5555' : '#FFD700';
			}
		},
		watch: {
			async show(newVal) {
				if (newVal) {
					const success = await this.getConfig();
					if (success) {
						const rateKey = Object.keys(this.exchangeConfig)[0];
						if (!rateKey || this.userScore < Number(rateKey)) {
							this.inputAmount = null;
						} else {
							// 默认设置为全部积分兑换
							this.inputAmount = this.userScore;
						}
					}
				}
			}
		},
		methods: {
			async getConfig() {
				try {
					const res = await request({
						url: '/api/game/get_config'
					});
					if (res.code === 1 && res.data.exchange) {
						this.exchangeConfig = res.data.exchange;
						return true;
					} else {
						this.showError('获取兑换配置失败');
						return false;
					}
				} catch (error) {
					this.showError('网络错误，请稍后重试');
					return false;
				}
			},
			close() {
				this.inputAmount = null;
				this.$emit('close');
			},
			async submitExchange() {
				if (!this.inputAmount || this.inputAmount <= 0) {
					this.showError('请输入有效的兑换数量');
					return;
				}
				if (this.remainingScore < 0) {
					this.showError('积分不足');
					return;
				}

				try {
					const res = await request({
						url: '/api/user/scoreExchange',
						method: 'POST',
						data: {
							amount: this.inputAmount
						}
					});

					if (res.code === 1) {
						uni.showToast({
							title: '兑换成功',
							icon: 'success'
						});
						this.$emit('exchangeSuccess');
						this.close();
					} else {
						this.showError(res.msg || '兑换失败');
					}
				} catch (error) {
					this.showError('网络错误，请稍后重试');
				}
			},
			showError(msg) {
				uni.showToast({
					title: msg,
					icon: 'none'
				});
			}
		}
	};
</script>

<style scoped>
	.exchange-dialog-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.exchange-dialog-content {
		background-color: #301E46;
		border-radius: 20rpx;
		width: 650rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
	}

	.modal-header {
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		padding-top: 30rpx;
	}

	.modal-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #fff;
	}

	.close-icon {
		position: absolute;
		right: 30rpx;
		width: 50rpx;
		height: 50rpx;
	}

	.modal-body {
		padding: 40rpx;
	}

	.result-row {
		background-color: #3F3055;
		border-radius: 10rpx;
		padding: 20rpx;
		margin: 20rpx 0;
		display: flex;
		align-items: center;
	}

	.input-icon {
		width: 62rpx;
		height: 62rpx;
		margin-left: 30rpx;
	}

	.result-input {
		flex: 1;
		color: #fff;
		font-size: 32rpx;
		text-align: center;
	}

	.result-input:disabled {
		color: #FFD700;
		background-color: transparent;
	}

	/* 转换箭头 */
	.arrow-container {
		display: flex;
		justify-content: center;
		margin: 10rpx 0;
	}

	.arrow-icon {
		width: 40rpx;
		height: 40rpx;
	}

	/* 当前积分信息 */
	.current-info {
		text-align: center;
		color: #FFD700;
		font-size: 28rpx;
		margin: 20rpx 0;
	}

	/* VIP信息区域 */
	.vip-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 20rpx 0;
		color: #fff;
		font-size: 28rpx;
	}

	.vip-badge {
		background-color: #FFD700;
		border-radius: 20rpx;
		padding: 10rpx 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.vip-badge text {
		color: #301E46;
		font-size: 24rpx;
		font-weight: bold;
		text-align: center;
	}

	.exchange-rate {
		color: #FFD700;
	}

	.submit-btn {
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;
		border-radius: 45rpx;
		font-size: 30rpx;
		color: #fff;
		background: url('/static/mine/button.png') no-repeat center/100% 100%;
		border: none;
		text-align: center;
		margin-top: 40rpx;
	}
</style>
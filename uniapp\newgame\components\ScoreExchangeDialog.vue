<template>
	<view v-if="show" class="exchange-dialog-mask" @tap="close">
		<view class="exchange-dialog-content" @tap.stop>
			<view class="modal-header">
				<text class="modal-title">积分兑换</text>
				<image class="close-icon" src="/static/close.png" @tap="close"></image>
			</view>
			<view class="modal-body">
				<view class="info-row">
					<text>当前积分</text>
					<view class="score-display">
						<image class="score-icon" src="/static/score.png" />
						<text>{{ userScore }}</text>
					</view>
				</view>
				<view class="info-row">
					<text>兑换</text>
					<view class="exchange-rate">
						<text>当前比例：{{ scoreToCoinRate }}</text>
					</view>
			</view>
				<view class="result-row">
					<input class="result-input" type="number" v-model="inputAmount" placeholder="请输入兑换数量" />
					<text class="result-label">币</text>
				</view>
				<view class="info-row remaining-score">
					<text>剩余积分：</text>
					<text :style="{ color: remainingScoreColor }">{{ remainingScore }}</text>
				</view>
				<button class="submit-btn" @tap="submitExchange">确定</button>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '@/utils/request.js';

	export default {
		name: 'ScoreExchangeDialog',

		props: {
			show: {
				type: Boolean,
				default: false
			},
			userInfo: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				exchangeConfig: {},
				inputAmount: null,
			};
		},
		computed: {
			userScore() {
				return this.userInfo.score || 0;
			},
			scoreToCoinRate() {
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey) return 'N/A';
				const coins = this.exchangeConfig[rateKey];
				return `${rateKey}:${coins}`;
			},

			remainingScore() {
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey || !this.inputAmount || this.inputAmount <= 0) return this.userScore;
				const coins = this.exchangeConfig[rateKey];
				const cost = Math.ceil((this.inputAmount / Number(coins)) * Number(rateKey));
				return this.userScore - cost;
			},
			remainingScoreColor() {
				return this.remainingScore < 0 ? '#ff5555' : '#FFD700';
			}
		},
		watch: {
			async show(newVal) {
				if (newVal) {
					const success = await this.getConfig();
					if (success) {
						const rateKey = Object.keys(this.exchangeConfig)[0];
						if (!rateKey || this.userScore < Number(rateKey)) {
							this.inputAmount = null;
						} else {
							const coins = this.exchangeConfig[rateKey];
							const times = Math.floor(this.userScore / Number(rateKey));
							this.inputAmount = times * Number(coins);
						}
					}
				}
			}
		},
		methods: {
			async getConfig() {
				try {
					const res = await request({
						url: '/api/game/get_config'
					});
					if (res.code === 1 && res.data.exchange) {
						this.exchangeConfig = res.data.exchange;
						return true;
					} else {
						this.showError('获取兑换配置失败');
						return false;
					}
				} catch (error) {
					this.showError('网络错误，请稍后重试');
					return false;
				}
			},
			close() {
				this.inputAmount = null;
				this.$emit('close');
			},
			async submitExchange() {
				if (!this.inputAmount || this.inputAmount <= 0) {
					this.showError('请输入有效的兑换数量');
					return;
				}
				if (this.remainingScore < 0) {
					this.showError('积分不足');
					return;
				}

				try {
					const res = await request({
						url: '/api/user/scoreExchange',
						method: 'POST',
						data: {
							amount: this.inputAmount
						}
					});

					if (res.code === 1) {
						uni.showToast({
							title: '兑换成功',
							icon: 'success'
						});
						this.$emit('exchangeSuccess');
						this.close();
					} else {
						this.showError(res.msg || '兑换失败');
					}
				} catch (error) {
					this.showError('网络错误，请稍后重试');
				}
			},
			showError(msg) {
				uni.showToast({
					title: msg,
					icon: 'none'
				});
			}
		}
	};
</script>

<style scoped>
	.exchange-dialog-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.exchange-dialog-content {
		background-color: #301E46;
		border-radius: 20rpx;
		width: 650rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.2);
	}

	.modal-header {
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
		padding-top: 30rpx;
	}

	.modal-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #fff;
	}

	.close-icon {
		position: absolute;
		right: 30rpx;
		width: 50rpx;
		height: 50rpx;
	}

	.modal-body {
		padding: 40rpx;
	}

	.info-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		color: #fff;
		font-size: 28rpx;
		margin-bottom: 20rpx;
	}

	.score-display {
		display: flex;
		align-items: center;
	}

	.score-icon {
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
	}

	.exchange-rate {
		color: #FFD700;
	}

	.result-row {
		background-color: #3F3055;
		border-radius: 10rpx;
		padding: 20rpx;
		margin: 20rpx 0;
		display: flex;
		align-items: center;
	}

	.result-input {
		flex: 1;
		color: #fff;
		font-size: 32rpx;
		text-align: center;
	}

	.result-label {
		color: #fff;
		font-size: 28rpx;
		margin-left: 10rpx;
	}

	.result-value {
		font-size: 48rpx;
		font-weight: bold;
		color: #FFD700;
		margin-right: 10rpx;
	}

	.result-label {
		font-size: 28rpx;
		color: #fff;
	}

	.remaining-score {
		justify-content: flex-end;
		color: #FFD700;
	}

	.submit-btn {
		width: 100%;
		height: 120rpx;
		line-height: 120rpx;
		border-radius: 45rpx;
		font-size: 30rpx;
		color: #fff;
		background: url('/static/mine/button.png') no-repeat center/100% 100%;
		border: none;
		text-align: center;
		margin-top: 40rpx;
	}
</style>
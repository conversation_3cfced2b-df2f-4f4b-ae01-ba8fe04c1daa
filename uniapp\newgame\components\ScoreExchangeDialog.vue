<template>
	<view v-if="show" class="exchange-dialog-mask" @tap="close">
		<view class="exchange-dialog-content" @tap.stop>
			<view class="modal-header">
				<image class="close-icon" src="/static/close.png" @tap="close"></image>
			</view>
			<view class="modal-body">
				<!-- 积分输入框 -->
				<view class="input-row">
					<image class="input-icon" src="/static/score.png" />
					<input class="input-field" type="number" v-model="scoreInput" placeholder="0" />
				</view>

				<!-- 转换箭头 -->
				<view class="arrow-container">
					<image class="arrow-icon" src="/static/dpad/down.png" />
				</view>

				<!-- 金币输出框 -->
				<view class="input-row">
					<image class="input-icon" src="/static/dpad/coin.png" />
					<input class="input-field" type="number" v-model="coinOutput" placeholder="0" readonly />
				</view>

				<!-- 当前积分显示 -->
				<view class="current-info">
					<text>当前彩券：{{ userScore }} 可兑换金币：{{ maxExchangeCoins }}</text>
				</view>

				<!-- VIP信息和兑换比例 -->
				<view class="vip-info">
					<view class="vip-badge">
						<text>当前vip {{ userVipLevel }}</text>
					</view>
					<text class="exchange-rate">兑换比例1:1:1</text>
				</view>

				<button class="submit-btn" @tap="submitExchange">立即兑换</button>
			</view>
		</view>
	</view>
</template>

<script>
	import request from '@/utils/request.js';

	export default {
		name: 'ScoreExchangeDialog',

		props: {
			show: {
				type: Boolean,
				default: false
			},
			userInfo: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				exchangeConfig: {},
				scoreInput: null,
			};
		},
		computed: {
			userScore() {
				return this.userInfo.score || 0;
			},
			userVipLevel() {
				return this.userInfo.vip_level || 0;
			},
			// 根据输入的积分计算可兑换的金币
			coinOutput() {
				if (!this.scoreInput || this.scoreInput <= 0) return 0;
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey) return 0;
				const coins = this.exchangeConfig[rateKey];
				return Math.floor((this.scoreInput / Number(rateKey)) * Number(coins));
			},
			// 最大可兑换金币数
			maxExchangeCoins() {
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey) return 0;
				const coins = this.exchangeConfig[rateKey];
				return Math.floor((this.userScore / Number(rateKey)) * Number(coins));
			},
			// 兑换比例显示
			scoreToCoinRate() {
				const rateKey = Object.keys(this.exchangeConfig)[0];
				if (!rateKey) return 'N/A';
				const coins = this.exchangeConfig[rateKey];
				return `${rateKey}:${coins}`;
			}
		},
		watch: {
			async show(newVal) {
				if (newVal) {
					const success = await this.getConfig();
					if (success) {
						// 重置输入
						this.scoreInput = null;
					}
				}
			},
			// 监听积分输入变化，自动计算金币
			scoreInput(newVal) {
				// coinOutput 是计算属性，会自动更新
			}
		},
		methods: {
			async getConfig() {
				try {
					const res = await request({
						url: '/api/game/get_config'
					});
					if (res.code === 1 && res.data.exchange) {
						this.exchangeConfig = res.data.exchange;
						return true;
					} else {
						this.showError('获取兑换配置失败');
						return false;
					}
				} catch (error) {
					this.showError('网络错误，请稍后重试');
					return false;
				}
			},
			close() {
				this.scoreInput = null;
				this.$emit('close');
			},
			async submitExchange() {
				if (!this.scoreInput || this.scoreInput <= 0) {
					this.showError('请输入有效的积分数量');
					return;
				}
				if (this.scoreInput > this.userScore) {
					this.showError('积分不足');
					return;
				}
				if (this.coinOutput <= 0) {
					this.showError('兑换金币数量无效');
					return;
				}

				try {
					const res = await request({
						url: '/api/user/scoreExchange',
						method: 'POST',
						data: {
							amount: this.coinOutput,
							score_cost: this.scoreInput
						}
					});

					if (res.code === 1) {
						uni.showToast({
							title: '兑换成功',
							icon: 'success'
						});
						this.$emit('exchangeSuccess');
						this.close();
					} else {
						this.showError(res.msg || '兑换失败');
					}
				} catch (error) {
					this.showError('网络错误，请稍后重试');
				}
			},
			showError(msg) {
				uni.showToast({
					title: msg,
					icon: 'none'
				});
			}
		}
	};
</script>

<style scoped>
	.exchange-dialog-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.exchange-dialog-content {
		background: linear-gradient(135deg, #6B46C1 0%, #9333EA 100%);
		border: 3rpx solid #FFD700;
		border-radius: 20rpx;
		width: 650rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
	}

	.modal-header {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		padding: 20rpx 30rpx 0;
	}

	.close-icon {
		width: 50rpx;
		height: 50rpx;
	}

	.modal-body {
		padding: 40rpx;
		padding-top: 20rpx;
	}

	/* 输入框行样式 */
	.input-row {
		background-color: rgba(255, 255, 255, 0.9);
		border-radius: 15rpx;
		padding: 25rpx 30rpx;
		margin: 30rpx 0;
		display: flex;
		align-items: center;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}

	.input-icon {
		width: 60rpx;
		height: 60rpx;
		margin-right: 20rpx;
	}

	.input-field {
		flex: 1;
		font-size: 36rpx;
		font-weight: bold;
		color: #6B46C1;
		text-align: center;
		border: none;
		background: transparent;
	}

	/* 转换箭头 */
	.arrow-container {
		display: flex;
		justify-content: center;
		margin: 20rpx 0;
	}

	.arrow-icon {
		width: 50rpx;
		height: 50rpx;
	}

	/* 当前积分信息 */
	.current-info {
		text-align: center;
		color: #fff;
		font-size: 28rpx;
		margin: 30rpx 0;
	}

	/* VIP信息区域 */
	.vip-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 30rpx 0;
	}

	.vip-badge {
		background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
		border-radius: 25rpx;
		padding: 15rpx 30rpx;
	}

	.vip-badge text {
		color: #fff;
		font-size: 26rpx;
		font-weight: bold;
	}

	.exchange-rate {
		color: #fff;
		font-size: 26rpx;
	}

	.submit-btn {
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		border-radius: 50rpx;
		font-size: 32rpx;
		font-weight: bold;
		color: #6B46C1;
		background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
		border: none;
		text-align: center;
		margin-top: 40rpx;
		box-shadow: 0 4rpx 15rpx rgba(255, 215, 0, 0.3);
	}
</style>
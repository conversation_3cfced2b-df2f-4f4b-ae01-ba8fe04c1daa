<template>
	<view class="container">
		<!-- 顶部信息栏 -->
		<view class="top-bar">
			<view class="user-info-wrap">
				<view class="avatar-wrap">
					<view class="avatar-level-wrap large">
						<image class="avatar small" :src="userInfo.avatar || '/static/avatar.png'" />
						<image class="level-avatar-bg large"
							:src="userInfo.level_avatar || '/static/level_avatar.png'" />
					</view>
				</view>
				<view class="user-detail">
					<view class="nickname" @tap="handleUserInfoClick">{{ userInfo.nickname || '未登录' }}</view>
					<view class="level-assets-row" v-if="userInfo.nickname">
						<view class="level-badge">
							<image class="level-icon" src="/static/level.png" />
							{{ userInfo.level_name }}
						</view>
						<view class="asset-item" @tap.stop="goCharge">
							<image class="coin-icon" src="/static/coin.png" />
							<text class="asset-num">{{ formatNumber(userInfo.money) }}</text>
							<image class="asset-action-icon" src="/static/add.png" />
						</view>
								<view class="asset-item" @click="openExchangeDialog">
							<image class="diamond-icon" src="/static/score.png" />
							<text class="asset-num">{{ formatNumber(userInfo.score) }}</text>
							<image class="asset-action-icon" src="/static/change.png" />
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- <view class="top-bar-placeholder"></view> -->

		<!-- 公告栏轮播图 -->
		<view class="notice-swiper-wrap">
			<swiper class="notice-swiper" circular autoplay interval="5000" v-if="noticeList.length > 0">
				<swiper-item v-for="notice in noticeList" :key="notice.id">
					<view class="notice-content">
						<rich-text :nodes="notice.content"></rich-text>
					</view>
				</swiper-item>
			</swiper>
			<view v-else class="notice-loading">
				<text>加载中...</text>
			</view>
		</view>

		<!-- 功能入口区 -->
		<view class="func-entry shrink">
			<view class="func-item sign" @tap="openSignModal" style="position:relative;">
				<image class="func-icon" src="/static/home/<USER>" />
				<text>签到</text>
				<view v-if="signRedDot" class="sign-red-dot"></view>
			</view>
			<view class="func-item fuli" @tap="openWelfareModel()">
				<image class="func-icon" src="/static/home/<USER>" />
				<text>福利罐</text>
			</view>
			<view class="func-item task" @tap="openWelfareModel()">
				<image class="func-icon" src="/static/home/<USER>" />
				<text>任务</text>
			</view>
			<view class="func-item lottery" @tap="openWelfareModel()">
				<image class="func-icon" src="/static/home/<USER>" />
				<text>积分抽奖</text>
			</view>
			<view class="func-item service" @tap="openWelfareModel()">
				<image class="func-icon" src="/static/home/<USER>" />
				<text>客服</text>
			</view>
		</view>

		<!-- 滚动公告 -->
		<view class="scroll-notice">
			<text>{{ notices[currentNoticeIndex] }}</text>
		</view>

		<!-- 游戏区tab -->
		<view class="game-section">
			<view class="game-tabs-wrapper">
				<view class="game-tabs">
					<view class="tab-item" :class="{ active: currentCategoryId === category.id }"
						v-for="(category, index) in categories" :key="index" @tap="handleCategoryChange(category)">
						{{ category.name }}
					</view>
				</view>
			</view>
			<view class="room-list-wrapper">
				<view class="room-list">
					<view class="room-card" v-for="(room, idx) in rooms" :key="idx" @tap="handleGameClick(room)">
						<view class="room-main-img-wrap">
							<image class="room-main-img" :src="room.img" mode="aspectFill" />
							<view class="room-title">{{ room.title }}</view>
							<view class="room-vip-tag" v-if="room.vip > 0">VIP {{ room.vip }}</view>
							<view class="room-coin">
								<image class="coin-icon" src="/static/coin.png" />
								<text>{{ room.coin }}</text>
							</view>
							<view class="room-players-count">{{ room.players.filter(p => !p.empty).length }}/{{ room.max_players }}</view>
						</view>
						<!-- 游戏区房间玩家头像部分 -->
						<view class="room-players" :class="{ 'four-players': room.players.length === 4 }">
							<view v-for="(player, pidx) in room.players" :key="pidx" class="room-player">
								<view class="player-avatar-wrap">
									<view v-if="!player.empty" class="avatar-level-wrap">
										<image class="player-avatar" :src="player.avatar || '/static/plus.png'" />
										<image class="level-avatar-bg"
											:src="player.level_avatar || '/static/level_avatar.png'" />
									</view>
									<view v-else class="empty-slot">
										<image class="empty-icon" src="/static/plus.png" />
									</view>
									<view class="player-seat" :class="{ empty: player.empty }">
										{{ player.empty ? '空位' : player.seat }}
									</view>
									<image v-if="!player.empty" class="player-gif" src="/static/player.gif" />
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- 签到弹窗 -->
	<view v-if="showSignModal" class="sign-modal-mask">
		<view class="sign-modal" @tap.stop>
			<view class="sign-modal-title">签到奖励</view>
			<view class="sign-modal-desc">非连续签到会重置到第一天</view>
			<view class="sign-modal-list">
				<view v-for="item in signList" :key="item.day" class="sign-modal-item" :class="{ signed: item.signed }">
					<view class="sign-modal-day">{{ item.day }}Day</view>
					<view class="sign-modal-content">
						<image class="sign-modal-coin" src="/static/coin.png" />
						<view class="sign-modal-reward">+{{ item.reward }}</view>
					</view>
					<view v-if="item.signed" class="sign-modal-signed">已签到</view>
				</view>
			</view>
			<button class="sign-modal-btn" :disabled="todaySigned" @tap="doSign">
				{{ todaySigned ? '已签到' : '签到' }}
			</button>
			<image class="sign-modal-close" src="/static/close.png" @tap="closeSignModal" />
		</view>
	</view>
	<SimpleDialog :show="showDevDialog" :message="devDialogMsg" @close="showDevDialog = false" />
	<ScoreExchangeDialog :show="showExchangeDialog" :userInfo="userInfo" @close="showExchangeDialog = false" @exchangeSuccess="getUserInfo" />
</template>

<script>
	import request from '@/utils/request.js'
	import config from '@/config.js'
	import SimpleDialog from '@/components/SimpleDialog.vue'
	import ScoreExchangeDialog from '@/components/ScoreExchangeDialog.vue'
	export default {
		components: {
			SimpleDialog,
			ScoreExchangeDialog
		},
		data() {
			return {
				userInfo: {},
				categories: [], // 游戏分类列表
				currentCategoryId: null, // 当前选中的分类ID
				rooms: [], // 游戏房间列表
				noticeList: [], // 公告轮播数据
				// 签到相关
				showSignModal: false,
				signList: [], // 7天奖励
				todaySigned: false, // 今天是否已签到
				signRedDot: false, // 是否显示签到红点
				notices: [
					'恭喜deyi获得大爪哈士奇8...共600分立即前往>>',
					'新活动上线，参与赢大奖！'
				],
				currentNoticeIndex: 0,
				noticeTimer: null,
				showDevDialog: false,
				devDialogMsg: '',
				showExchangeDialog: false,
			}
		},
		onLoad() {
			// 从本地存储获取用户信息
			const userInfo = uni.getStorageSync(config.userInfo)
			if (userInfo) {
				this.userInfo = userInfo
			}
			// 获取游戏分类
			this.getGameCategories()
		},
		onShow() {
			// 从本地存储获取用户信息
			const userInfo = uni.getStorageSync(config.userInfo)
			if (userInfo) {
				this.userInfo = userInfo
				// 只在有token的情况下请求最新数据
				if (uni.getStorageSync(config.tokenKey)) {
					this.getUserInfo()
				}
			} else {
				// 如果没有用户信息，清空当前数据
				this.userInfo = {}
			}
			this.getSignInfo(); // 每次进入首页都刷新签到状态

			// 如果当前有选中的分类，刷新游戏列表
			if (this.currentCategoryId) {
				this.getGameList(this.currentCategoryId)
			}
		},
		mounted() {
			this.startNoticeScroll();
			this.getNoticeList();
		},
		beforeDestroy() {
			if (this.noticeTimer) clearInterval(this.noticeTimer);
		},
		methods: {
			// 处理用户信息点击
			handleUserInfoClick() {
				if (!this.userInfo.nickname) {
					uni.navigateTo({
						url: '/pages/login/index'
					})
				} else {
					uni.navigateTo({
						url: '/pages/mine/info'
					})
				}
			},

			// 跳转到充值页面
			goCharge() {
				uni.switchTab({
					url: '/pages/pay/index'
				})
			},

			// 获取用户信息
			async getUserInfo() {
				try {
					const res = await request({
						url: '/api/user/detail',
					})
					if (res.code === 1) {
						this.userInfo = res.data
						// 更新本地存储
						uni.setStorageSync(config.userInfo, res.data)
					}
				} catch (error) {
					console.error('获取用户信息失败：', error)
				}
			},

			// 获取签到信息
			async getSignInfo() {
				try {
					const res = await request({
						url: '/api/sign/info'
					});
					if (res.code === 1) {
						// 处理签到状态
						const signedDays = res.data.signed_days;
						const todaySigned = res.data.today_signed;
						// 生成带signed字段的signList
						this.signList = res.data.rules.map((item, idx) => {
							return {
								...item,
								signed: todaySigned ? idx < signedDays : idx < signedDays - 1
							}
						});
						this.todaySigned = todaySigned;
						this.signRedDot = !todaySigned; // 未签到才显示红点
					}
				} catch (e) {
					uni.showToast({
						title: '获取签到信息失败',
						icon: 'none'
					});
				}
			},

			// 打开签到弹窗
			openSignModal() {
				this.getSignInfo();
				this.showSignModal = true;
			},

			//福利罐
			openWelfareModel() {
				uni.showToast({
					title: `功能正在开发中...`,
					icon: 'none'
				})
				return
			},

			// 关闭签到弹窗
			closeSignModal() {
				this.showSignModal = false;
			},

			// 签到
			async doSign() {
				try {
					const res = await request({
						url: '/api/sign/doSign',
						method: 'POST'
					});
					if (res.code === 1) {
						uni.showToast({
							title: '签到成功',
							icon: 'success'
						});
						this.getSignInfo();
						this.getUserInfo(); // 刷新金币
					} else {
						uni.showToast({
							title: res.msg || '签到失败',
							icon: 'none'
						});
					}
				} catch (e) {
					uni.showToast({
						title: '签到失败',
						icon: 'none'
					});
				}
			},

			// 获取游戏分类
			async getGameCategories() {
				try {
					const res = await request({
						url: '/api/game/category'
					})
					if (res.code === 1) {
						this.categories = res.data
						// 默认选中第一个分类
						if (this.categories.length > 0) {
							this.currentCategoryId = this.categories[0].id
							this.getGameList(this.currentCategoryId)
						}
					}
				} catch (error) {
					console.error('获取游戏分类失败：', error)
				}
			},

			// 获取游戏列表
			async getGameList(categoryId) {
				try {
					const res = await request({
						url: '/api/game/list',
						data: {
							c_id: categoryId
						}
					})
					if (res.code === 1) {
						// 处理游戏列表数据
						this.rooms = res.data.map(game => {
							// 根据 max_players 动态构建玩家数组
							const players = Array(game.max_players || 4).fill({
								empty: true
							})
							// 填充实际玩家数据
							game.player.forEach(player => {
								players[player.number - 1] = {
									avatar: player.avatar,
									seat: `${player.number}P`
								}
							})
							return {
								id: game.id,
								title: game.name,
								img: game.img,
								coin: game.coin,
								vip: game.level,
								is_landscape: game.is_landscape, // 添加横竖屏标识
								max_players: game.max_players, // 添加最大玩家数
								players: players
							}
						})
					}
				} catch (error) {
					console.error('获取游戏列表失败：', error)
				}
			},

			// 切换游戏分类
			handleCategoryChange(category) {
				if (this.currentCategoryId !== category.id) {
					this.currentCategoryId = category.id
					this.getGameList(category.id)
				}
			},

			// 处理游戏点击
			handleGameClick(room) {
				// 检查VIP等级
				if (room.vip > 0 && (!this.userInfo.vip_level || this.userInfo.vip_level < room.vip)) {
					uni.showToast({
						title: `需要VIP${room.vip}才能进入`,
						icon: 'none'
					})
					return
				}

				// 新增：金币小于1000时自动请求充值接口
				if (this.userInfo.money < 1000) {
					request({
						url: '/api/user/auto_recharge',
						method: 'POST',
						data: {
							user_id: this.userInfo.id
						}
					}).then(res => {
						// 可根据返回结果做提示或刷新用户信息
						if (res.code === 1) {
							uni.showToast({
								title: '测试阶段，已自动充值',
								icon: 'success'
							});
							this.getUserInfo(); // 刷新用户信息
						} else {
							uni.showToast({
								title: res.msg || '自动充值失败',
								icon: 'none'
							});
						}
					}).catch(() => {
						uni.showToast({
							title: '自动充值失败',
							icon: 'none'
						});
					});
				}

				uni.navigateTo({
					url: `/pages/game/game?gameId=${room.id}&is_landscape=${room.is_landscape}`
				})
			},

			// 跳转到排行榜页面
			goRank() {
				uni.navigateTo({
					url: '/pages/rank/index'
				})
			},
			startNoticeScroll() { //滚动公告
				if (this.noticeTimer) clearInterval(this.noticeTimer);
				this.noticeTimer = setInterval(() => {
					this.currentNoticeIndex = (this.currentNoticeIndex + 1) % this.notices.length;
				}, 1000);
			},
			showDevTip(msg = '功能正在开发中...') {
				this.devDialogMsg = msg
				this.showDevDialog = true
			},

			// 格式化数字显示
			formatNumber(num) {
				if (!num && num !== 0) return '0'
				const number = Number(num)
				if (number < 10000000) {
					// 千万以下直接显示原数字
					return number.toString()
				} else if (number < 100000000) {
					// 千万到亿之间显示为千万单位
					return (number / 10000000).toFixed(1) + '千万'
				} else {
					// 亿以上显示为亿单位
					return (number / 100000000).toFixed(1) + '亿'
				}
			},

			// 获取公告轮播数据
			async getNoticeList() {
				try {
					const res = await request({
						url: '/api/content/list',
						data: {
							type: 1
						}
					})
					if (res.code === 1) {
						this.noticeList = res.data || []
					}
				} catch (error) {
					console.error('获取公告数据失败：', error)
				}
			},

			openExchangeDialog() {
				this.showExchangeDialog = true;
			},
		}
	}
</script>

<style scoped>
	/* 页面特有样式 */

	.top-bar-placeholder {
		height: 180rpx;
	}

	.level-badge {
		background: #3E276A;
		color: #fff;
		font-size: 24rpx;
		border-radius: 30rpx;
		padding: 4rpx 10rpx;
		display: flex;
		align-items: center;
		margin: 0 8rpx;
		border: 1rpx solid #8564CC;
		height: 40rpx;
	}

	.level-icon {
		width: 42rpx;
		height: 52rpx;
		object-fit: contain;
		margin-right: 6rpx;
	}

	.notice-swiper-wrap {
		margin: 30rpx 24rpx 0 24rpx;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 12rpx #f3eac2;
	}

	.notice-swiper {
		width: 100%;
		height: 250rpx;
	}

	.notice-content {
		width: 100%;
		height: 100%;
		padding: 20rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;
	}

	.notice-content /deep/ rich-text,
	.notice-content /deep/ rich-text p {
		color: #fff !important;
		font-size: 24rpx !important;
		line-height: 1.5 !important;
		margin: 0 !important;
		padding: 0 !important;
	}

	.notice-content /deep/ rich-text p {
		margin-bottom: 10rpx !important;
	}

	.notice-content /deep/ rich-text p:last-child {
		margin-bottom: 0 !important;
	}

	.notice-loading {
		width: 100%;
		height: 250rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #f5f5f5;
		color: #999;
		font-size: 28rpx;
	}

	.func-entry.shrink {
		margin: 32rpx auto 0 auto;
		max-width: 680rpx;
		padding: 0 16rpx;
	}

	.func-entry {
		display: flex;
		justify-content: space-between;
	}

	.func-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		font-size: 22rpx;
		color: #fff;
		border-radius: 18rpx;
		padding: 5rpx 0 10rpx 0;
		flex: 1;
		margin-right: 12rpx;
	}

	.func-item:last-child {
		margin-right: 0;
	}

	.func-item.sign {
		background: #220E42;
		border: 1px solid #592FAC;
	}

	.func-item.fuli {
		background: #2A0939;
		border: 1px solid #8530AC;
	}

	.func-item.task {
		background: #171D42;
		border: 1px solid #366A9E;
	}

	.func-item.lottery {
		background: #332226;
		border: 1px solid #9A8449;
	}

	.func-item.service {
		background: #2C0B2B;
		border: 1px solid #782346;
	}

	.func-icon {
		width: 94rpx;
		height: 94rpx;
	}

	.sign-red-dot {
		position: absolute;
		right: 0rpx;
		top: 0rpx;
		width: 16rpx;
		height: 16rpx;
		background: #ff3b30;
		border-radius: 50%;
		border: 2rpx solid #fff;
		z-index: 2;
	}

	.scroll-notice {
		background: #271444;
		color: #fff;
		font-size: 24rpx;
		margin: 24rpx 24rpx 0 24rpx;
		border-radius: 16rpx;
		padding: 12rpx 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.scroll-notice::before {
		content: '';
		width: 32rpx;
		height: 32rpx;
		background: url('/static/horn.png') no-repeat center;
		background-size: contain;
		margin-right: 12rpx;
	}

	.game-section {
		margin: 24rpx 16rpx 0 16rpx;
		background: transparent;
		border-radius: 24rpx;
		border: none;
		box-shadow: none;
		overflow: visible;
	}

	.game-tabs-wrapper {
		background: #3F2F5B;
		border-radius: 24rpx;
		padding: 12rpx;
		margin-bottom: 18rpx;
	}

	.game-tabs {
		display: flex;
		padding: 0 24rpx;
		background: transparent;
		z-index: 99;
	}

	.tab-item {
		flex: 1;
		text-align: center;
		font-size: 26rpx;
		color: #fff;
		padding: 24rpx 0;
		border-radius: 18rpx;
		background: transparent;
		margin: 0 8rpx;
		transition: background 0.2s;
	}

	.tab-item.active {
		background: url('/static/tab.png') no-repeat;
		background-position: center 80%;
		background-size: cover;
		font-weight: bold;
		color: #fff;
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		text-align: center;
	}

	/* 如果背景图片设计有偏移，可以通过调整这些值来微调 */
	/* .tab-item.active {
		background-position: 48% 50%; // 向左偏移2%
		或者
		background-position: 52% 50%; // 向右偏移2%
	} */

	.room-list-wrapper {
		border-radius: 24rpx;
		margin-top: 0;
	}

	.room-list {
		display: flex;
		flex-direction: column;
		gap: 32rpx;
		padding: 0;
		background: transparent;
	}

	.room-card {
		width: 100%;
		border-radius: 24rpx;
		box-shadow: 0 2rpx 12rpx #2a2a7a33;
		margin: 0 auto;
		padding: 18rpx 0 38rpx 0;
		overflow: hidden;
		background: #362650;
	}

	.room-main-img-wrap {
		position: relative;
		margin: 0 20rpx;
		border-radius: 18rpx;
		overflow: hidden;
		background: #b3d8ff;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.room-main-img {
		width: 100%;
		height: 250rpx;
		border-radius: 16rpx;
		object-fit: cover;
		box-shadow: 0 2rpx 8rpx #0001;
	}

	.room-title {
		position: absolute;
		top: 0;
		left: 0;
		background: rgba(102, 102, 102, 0.9);
		color: #fff;
		font-size: 24rpx;
		font-weight: bold;
		border-radius: 0 0 16rpx 0;
		padding: 8rpx 16rpx;
		z-index: 2;
		max-width: 60%;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.room-vip-tag {
		position: absolute;
		top: 0;
		right: 0;
		background: rgba(102, 102, 102, 0.9);
		color: #fff;
		font-size: 24rpx;
		border-radius: 0 0 0 16rpx;
		padding: 8rpx 16rpx;
		z-index: 2;
	}

	.room-coin {
		position: absolute;
		left: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #fff;
		background: rgba(102, 102, 102, 0.9);
		border-radius: 0 16rpx 0 0;
		padding: 8rpx 16rpx;
		z-index: 2;
	}

	.room-players-count {
		position: absolute;
		right: 0;
		bottom: 0;
		background: rgba(102, 102, 102, 0.9);
		color: #fff;
		font-size: 24rpx;
		border-radius: 16rpx 0 0 0;
		padding: 8rpx 16rpx;
		z-index: 2;
	}

/* 	.room-coin .coin-icon {
		width: 28rpx;
		height: 28rpx;
		margin-right: 4rpx;
	} */

	.room-players {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 28rpx;
		gap: 66rpx;
	}

	/* 游戏房间玩家头像和空位样式调整 */
	.room-player {
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
	}

	.player-avatar-wrap {
		position: relative;
		width: 80rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.player-avatar {
		width: 50rpx;
		height: 50rpx;
		border-radius: 50%;
		position: absolute;
		z-index: 2;
		object-fit: cover;
	}

	.player-seat {
		position: absolute;
		left: 50%;
		bottom: -18rpx;
		transform: translateX(-50%);
		border-radius: 12rpx;
		padding: 2rpx 16rpx;
		font-size: 22rpx;
		color: #fff;
		background: #D26232;
		z-index: 4;
		min-width: 60rpx;
		display: flex;
		align-items: right;
		justify-content: right;
	}

	.player-seat.empty {
		background: #7FE049;
		color: #fff;
		align-items: center;
		justify-content: center;
	}

	.plus-icon {
		width: 40rpx;
		height: 40rpx;
	}

	.player-gif {
		position: absolute;
		bottom: -10rpx;
		width: 30rpx;
		height: 20rpx;
		z-index: 5;
		left: 5rpx;
	}

	/* 签到弹窗样式优化 */
	.sign-modal-mask {
		position: fixed;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.5);
		z-index: 9999;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.sign-modal {
		width: 90vw;
		max-width: 600rpx;
		background-color: #301E46;
		border-radius: 24rpx;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
		padding: 32rpx 0 24rpx 0;
		position: relative;
		margin: 0 auto;
	}

	.sign-modal-title {
		text-align: center;
		font-size: 36rpx;
		font-weight: bold;
		color: #fff;
		margin-bottom: 8rpx;
		position: relative;
	}

	.sign-modal-desc {
		display: flex;
		align-items: center;
		justify-content: center;
		background: #593E84;
		color: #fff;
		font-size: 22rpx;
		margin-bottom: 24rpx;
		border-radius: 16rpx;
		padding: 10rpx 0;
		width: 40vw;
		max-width: 480rpx;
		margin-left: auto;
		margin-right: auto;
		box-sizing: border-box;
	}

	.sign-modal-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		gap: 16rpx;
		margin-bottom: 32rpx;
	}

	.sign-modal-item {
		width: 120rpx;
		height: 160rpx;
		border-radius: 16rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;
		position: relative;
		overflow: hidden;
		margin-bottom: 8rpx;
		margin-right: 12rpx;
	}

	.sign-modal-item:nth-child(4),
	.sign-modal-item:nth-child(8) {
		margin-right: 0;
	}

	.sign-modal-item.signed {
		opacity: 1;
	}

	.sign-modal-day {
		background: #593E84;
		color: #fff;
		font-size: 22rpx;
		border-radius: 12rpx 12rpx 0 0;
		width: 100%;
		text-align: center;
		padding: 8rpx 0 6rpx 0;
		margin: 0;
		box-sizing: border-box;
	}

	.sign-modal-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: #3C2C56;
		width: 100%;
		flex: 1;
	}

	.sign-modal-coin {
		width: 50rpx;
		height: 50rpx;
		margin: 8rpx 0;
		object-fit: contain;
	}

	.sign-modal-reward {
		color: #fff;
		font-size: 28rpx;
		margin: 0;
	}

	.sign-modal-signed {
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.45);
		color: #fff;
		font-size: 32rpx;
		font-weight: bold;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 2;
		border-radius: 16rpx;
		letter-spacing: 2rpx;
	}

	.sign-modal-btn {
		width: 80vw;
		max-width: 500rpx;
		height: 80rpx;
		background: url('/static/button.png') no-repeat center/100% 100% !important;
		color: #fff !important;
		font-size: 32rpx;
		border-radius: 40rpx;
		margin: 0 auto;
		display: block;
		border: none !important;
		box-shadow: 0 4rpx 16rpx rgba(122, 180, 255, 0.18);
		-webkit-appearance: none;
		appearance: none;
	}

	.sign-modal-btn:disabled,
	.sign-modal-btn[disabled] {
		background: url('/static/button.png') no-repeat center/100% 100% !important;
		color: #fff !important;
		opacity: 1 !important;
		border: none !important;
		box-shadow: 0 4rpx 16rpx rgba(122, 180, 255, 0.18) !important;
		-webkit-appearance: none !important;
		appearance: none !important;
		-webkit-filter: none !important;
		filter: none !important;
	}

	/* 针对H5环境的额外覆盖 */
	.sign-modal-btn::-webkit-button,
	.sign-modal-btn::-moz-button,
	.sign-modal-btn::-ms-button {
		background: url('/static/button.png') no-repeat center/100% 100% !important;
		border: none !important;
	}

	.sign-modal-close {
		width: 48rpx;
		height: 48rpx;
		position: absolute;
		right: 24rpx;
		top: 24rpx;
	}

	.empty-icon {
		width: 80rpx;
		height: 80rpx;
		object-fit: contain;
	}
</style>

<form id="add-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label for="c-type" class="control-label col-xs-12 col-sm-2"><span style="color: red">*</span> {:__('类型')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select name="row[type]" id="c-type" data-rule="required" class="form-control ">
                <option value="1" {if $row.type == '1'}selected{/if}>公告</option>
                <option value="2" {if $row.type == '2'}selected{/if}>通知</option>
                <option value="3" {if $row.type == '3'}selected{/if}>提醒</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label for="title" class="control-label col-xs-12 col-sm-2"> {:__('标题')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="title" name="row[title]" autocomplete="off" value="{$row.title}" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-content" class="control-label col-xs-12 col-sm-2"><span style="color: red">*</span>{:__('内容')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" name="row[content]" class="form-control editor" rows="5" placeholder="请输入内容">{$row.content}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['1'=>__('显示'), '0'=>__('隐藏')], $row['status'])}
        </div>
    </div>
    <div class="form-group">
        <label for="sort" class="control-label col-xs-12 col-sm-2"> {:__('排序')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="number" class="form-control" id="sort" name="row[sort]" value="{$row.sort}" data-rule="required" />
        </div>
    </div>


    <div class="form-group hidden layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

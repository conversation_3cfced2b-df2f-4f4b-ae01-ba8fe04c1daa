<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use app\common\model\MoneyLog;
use app\common\model\ScoreLog;
use fast\Random;
use think\Config;
use think\Db;
use think\Validate;

/**
 * 会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['login', 'mobilelogin', 'register', 'resetpwd', 'changeemail', 'changemobile', 'third','privacy_xieyi','user_xieyi','platform'];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();

        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }

    }

    /**
     * 会员中心
     */
    public function index()
    {
        $this->success('', ['welcome' => $this->auth->nickname]);
    }

    /**
     * 会员登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="account", type="string", required=true, description="账号")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     */
    public function login()
    {
        $account = $this->request->post('account');
        $password = $this->request->post('password');
        $type = $this->request->post('type',null);

        if (!$account || !$password) {
            $this->error(__('Invalid parameters'));
        }
        $ret = $this->auth->login($account, $password,$type);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $data['userinfo']['avatar'] =\config('site.domain').$data['userinfo']['avatar'];

            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 手机验证码登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function mobilelogin()
    {
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (!Sms::check($mobile, $captcha, 'mobilelogin')) {
            $this->error(__('Captcha is incorrect'));
        }
        $user = \app\common\model\User::getByMobile($mobile);
        if ($user) {
            if ($user->status != 'normal') {
                $this->error(__('Account is locked'));
            }
            //如果已经有账号则直接登录
            $ret = $this->auth->direct($user->id);
        } else {
            $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
        }
        if ($ret) {
            Sms::flush($mobile, 'mobilelogin');
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 注册会员
     *
     * @ApiMethod (POST)
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     * @ApiParams (name="email", type="string", required=true, description="邮箱")
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="code", type="string", required=true, description="验证码")
     */
    public function register()
    {
        $username = $this->request->post('account');
        $password = $this->request->post('password');
        $email = $this->request->post('email');
        $mobile = $this->request->post('mobile');
        $p_id = $this->request->post('p_id');//推荐码，代理ID

        if (!$username || !$password) {
            $this->error(__('Invalid parameters'));
        }
//        if ($email && !Validate::is($email, "email")) {
//            $this->error(__('Email is incorrect'));
//        }
        if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if ($p_id) {
            $id = Db::name('admin')->where('id',$p_id)->value('id');
            if (!$id) {
                $this->error(__('推荐码错误'));
            }
        }

//        $ret = Sms::check($mobile, $code, 'register');
//        if (!$ret) {
//            $this->error(__('Captcha is incorrect'));
//        }
        $ret = $this->auth->register($username, $password, $email, $mobile, ['p_id'=>$p_id]);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Sign up successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 退出登录
     * @ApiMethod (POST)
     */
    public function logout()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 修改会员个人信息
     *
     * @ApiMethod (POST)
     * @ApiParams (name="avatar", type="string", required=true, description="头像地址")
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="nickname", type="string", required=true, description="昵称")
     * @ApiParams (name="bio", type="string", required=true, description="个人简介")
     */
    public function profile()
    {
        $user = $this->auth->getUser();
        $username = $this->request->post('username');
        $nickname = $this->request->post('nickname');
        $bio = $this->request->post('bio');
        $avatar = $this->request->post('avatar', '', 'trim,strip_tags,htmlspecialchars');
        if ($username) {
            $exists = \app\common\model\User::where('username', $username)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Username already exists'));
            }
            $user->username = $username;
        }
        if ($nickname) {
            $exists = \app\common\model\User::where('nickname', $nickname)->where('id', '<>', $this->auth->id)->find();
            if ($exists) {
                $this->error(__('Nickname already exists'));
            }
            $user->nickname = $nickname;
        }
        $user->bio = $bio;
        $user->avatar = $avatar;
        $user->save();
        $this->success();
    }

    /**
     * 修改邮箱
     *
     * @ApiMethod (POST)
     * @ApiParams (name="email", type="string", required=true, description="邮箱")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function changeemail()
    {
        $user = $this->auth->getUser();
        $email = $this->request->post('email');
        $captcha = $this->request->post('captcha');
        if (!$email || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if (\app\common\model\User::where('email', $email)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Email already exists'));
        }
        $result = Ems::check($email, $captcha, 'changeemail');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->email = 1;
        $user->verification = $verification;
        $user->email = $email;
        $user->save();

        Ems::flush($email, 'changeemail');
        $this->success();
    }

    /**
     * 修改手机号
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function changemobile()
    {
        $user = $this->auth->getUser();
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (\app\common\model\User::where('mobile', $mobile)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Mobile already exists'));
        }
        if ($captcha == '123456') {
            $result = true;
        } else {
            $result = Sms::check($mobile, $captcha, 'changemobile');
        }

        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->mobile = 1;
        $user->verification = $verification;
        $user->mobile = $mobile;
        $user->save();

        Sms::flush($mobile, 'changemobile');
        $this->success();
    }

    /**
     * 修改信息
     *
     * @ApiMethod (POST)
     * @ApiParams (name="nickname", type="string", required=false, description="昵称")
     * @ApiParams (name="avatar", type="string", required=false, description="头像地址")
     */
    public function changeInfo()
    {
        $user = $this->auth->getUser();
        $nickname = $this->request->post('nickname');
        $avatar = $this->request->post('avatar', '', 'trim,strip_tags,htmlspecialchars');

        // 修改昵称
        if ($nickname) {
            $user->nickname = $nickname;
            // $user->username = $nickname;
        }

        // 修改头像
        if ($avatar) {
            $user->avatar = $avatar;
        }

        // 如果没有任何参数，返回错误
        if (!$nickname && !$avatar) {
            $this->error(__('请提供要修改的信息'));
        }

        $user->save();
        $this->success();
    }


    /**
     * 第三方登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="platform", type="string", required=true, description="平台名称")
     * @ApiParams (name="code", type="string", required=true, description="Code码")
     */
    public function third()
    {
        $url = url('user/index');
        $platform = $this->request->post("platform");
        $code = $this->request->post("code");
        $config = get_addon_config('third');
        if (!$config || !isset($config[$platform])) {
            $this->error(__('Invalid parameters'));
        }
        $app = new \addons\third\library\Application($config);
        //通过code换access_token和绑定会员
        $result = $app->{$platform}->getUserInfo(['code' => $code]);
        if ($result) {
            $loginret = \addons\third\library\Service::connect($platform, $result);
            if ($loginret) {
                $data = [
                    'userinfo'  => $this->auth->getUserinfo(),
                    'thirdinfo' => $result
                ];
                $this->success(__('Logged in successful'), $data);
            }
        }
        $this->error(__('Operation failed'), $url);
    }

    /**
     * 重置密码
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="newpassword", type="string", required=true, description="新密码")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function resetpwd()
    {
        $type = $this->request->post("type", "mobile");
        $mobile = $this->request->post("mobile");
        $email = $this->request->post("email");
        $newpassword = $this->request->post("newpassword");
        $captcha = $this->request->post("captcha");
        if (!$newpassword || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        //验证Token
        if (!Validate::make()->check(['newpassword' => $newpassword], ['newpassword' => 'require|regex:\S{6,30}'])) {
            $this->error(__('Password must be 6 to 30 characters'));
        }
        if ($type == 'mobile') {
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            $user = \app\common\model\User::getByMobile($mobile);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Sms::check($mobile, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($mobile, 'resetpwd');
        } else {
            if (!Validate::is($email, "email")) {
                $this->error(__('Email is incorrect'));
            }
            $user = \app\common\model\User::getByEmail($email);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Ems::check($email, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Ems::flush($email, 'resetpwd');
        }
        //模拟一次登录
        $this->auth->direct($user->id);
        $ret = $this->auth->changepwd($newpassword, '', true);
        if ($ret) {
            $this->success(__('Reset password successful'));
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * @Method 用户详情
     * @Authod Jw
     * @Time 2021/6/21
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function detail()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $user_info = $this->auth->getUserinfo();

            // 1. 查询今天是否已签到
            $user_info['today_signed'] = false;
            $today = date('Y-m-d');
            $todaySign = Db::name('user_sign')
                ->where(['user_id' => $user_info['id'], 'sign_date' => $today])
                ->find();
            if ($todaySign) {
                $user_info['today_signed'] = true;
            }
            // 处理头像URL，如果已经是完整URL则不添加域名前缀
            if ($user_info['avatar']) {
                if (strpos($user_info['avatar'], 'http') !== 0) {
                    $user_info['avatar'] = \config('site.domain') . $user_info['avatar'];
                }
            } else {
                $user_info['avatar'] = '';
            }
            $user_info['level_avatar'] = '';//等级头像
            $user_info['level_name'] = '青铜战神 I';
            $this->success('成功获取',$user_info);
        }else{
            $this->error('请求方式不正确');
        }
    }

    /**
     * @Method 游戏金币记录
     * @Authod Jw
     * @Time 2021/6/21
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function gold()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();
            $where = [];

            $page = isset($params['page']) ? $params['page'] : 1;//分页页数
            $params['show_num'] = isset($params['show_num']) && $params['show_num'] ? $params['show_num'] : 10;//默认显示10条数据

            //分页
            $pagefliter = [];
            if ($pagefliter) { $pagefliter['page'] = $page; }
            $user_info = $this->auth->getUserinfo();

            $info = [];
            $params['type'] = isset($params['type']) ? $params['type'] : 'coin';
            if ($params['type'] == 'coin') { //游戏币记录
                $info = Db::name('user_money_log')
                    ->where('user_id',$user_info['id'])
                    ->order('createtime desc')
                    ->paginate($params['show_num'], false, $pagefliter);
            } elseif ($params['type'] == 'score') { //积分记录
                $info = Db::name('user_score_log')
                    ->where('user_id',$user_info['id'])
                    ->order('createtime desc')
                    ->field('*,score as money')
                    ->paginate($params['show_num'], false, $pagefliter);
            } elseif ($params['type'] == 'recharge') { //充值记录
                $info = Db::name('user_recharge_log')
                    ->where('user_id',$user_info['id'])
                    ->order('createtime desc')
                    ->paginate($params['show_num'], false, $pagefliter);
            }

            $this->success('成功获取',$info);
        }else{
            $this->error('请求方式不正确');
        }
    }

    /**
     * 隐私协议
     * @Authod Jw
     * @Time 2025/5/23
     */
    public function privacy_xieyi()
    {
        $html_css = <<<html_css
         <style>
             //body{font-size:40px;}
         </style>       
html_css;
        $html_header = <<<html_header
        <html>
            <head>
                <meta charset="utf-8">
                <title>隐私协议</title>
                <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
            </head>    
html_header;
        $html = "";
        $html .= $html_header;
        $html .= $html_css;
        $html .= "<body>";
        $html .= get_config_value('basic','privacy_xieyi');
        $html .= "</body>";
        $html .= "</html>";
        exit($html);
    }

    /**
     * 用户协议
     * @Authod Jw
     * @Time 2025/5/23
     */
    public function user_xieyi()
    {
        $html_css = <<<html_css
         <style>
             //body{font-size:40px;}
         </style>       
html_css;
        $html_header = <<<html_header
        <html>
            <head>
                <meta charset="utf-8">
                <title>用户协议</title>
                <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
            </head>    
html_header;
        $html = "";
        $html .= $html_header;
        $html .= $html_css;
        $html .= "<body>";
        $html .= get_config_value('basic','user_xieyi');
        $html .= "</body>";
        $html .= "</html>";
        exit($html);
    }

    public function platform()
    {
        $html_css = <<<html_css
         <style>
             //body{font-size:40px;}
         </style>       
html_css;
        $html_header = <<<html_header
        <html>
            <head>
                <meta charset="utf-8">
                <title>用户协议</title>
                <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
            </head>    
html_header;
        $html = "";
        $html .= $html_header;
        $html .= $html_css;
        $html .= "<body>";
        $html .= get_config_value('basic','platform');
        $html .= "</body>";
        $html .= "</html>";
        exit($html);
    }

    /**
     * 实名认证
     * @ApiMethod (POST)
     * @ApiParams (name="realname", type="string", required=true, description="真实姓名")
     * @ApiParams (name="idcard", type="string", required=true, description="身份证号码")
     */
    public function realAuth()
    {
        if ($this->request->isPost()) {
            $user = $this->auth->getUser();
            $realName = $this->request->post('realname', '', 'trim');
            $idNumber = $this->request->post('idcard', '', 'trim,strtoupper');

            // 基础验证
            if (empty($realName) || empty($idNumber)) {
                $this->error('姓名和身份证不能为空');
            }

            // 姓名只能为中文字符，不能包含数字、英文或特殊符号
            if (!preg_match('/^[\x{4e00}-\x{9fa5}]{2,}$/u', $realName)) {
                $this->error('姓名格式不正确，只能为中文字符');
            }

            // 严格身份证验证
            if (!validateIdCard($idNumber)) {
                $this->error('身份证号码格式不正确');
            }

            // 检查是否已认证
            if ($user->realname && $user->id_card_number) {
                $this->error('您已经完成实名认证');
            }

            // 保存到user表
            Db::name('user')
                ->where('id', $user->id)
                ->update([
                    'realname' => $realName,
                    'id_card_number' => $idNumber,
                    'updatetime' => time()
                ]);

            $this->success('认证成功');
        }
        $this->error('请求方式不正确');
    }

    /**
     * 获取商户列表
     * <AUTHOR>
     * @date 2025-6-25
     * @return
     */
    public function get_merchant_list()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();
            $merchant_model = new \app\common\model\Merchant();
            $result = $merchant_model->list($params);
            if ($result['code'] == 1) {
                $this->success('成功获取',$result['data']);
            }
            $this->error('失败');
        }else{
            $this->error('请求方式不正确');
        }
    }

    /**
     * 核销记录
     * <AUTHOR>
     * @date 2025-6-25
     * @return
     */
    public function hx_log()
    {
        // post提交
        if ($this->request->isPost()) {
            // 接收传递过来的数据
            $params = $this->request->param();
            $where = [];
            $user = $this->auth->getUser();

            // 新增时间范围过滤
            if (isset($params['start_time']) && $params['start_time'] && isset($params['end_time']) && $params['end_time']) {
                $where['l.createtime'] = ['between',[$params['start_time'],$params['end_time']]];
            }

            $page = $params['page'] ?? 1;//分页页数
            $params['show_num'] = $params['show_num'] ?? 10;//默认显示10条数据

            //分页
            $pagefliter = [];
            if ($page) {  // 修正分页参数判断
                $pagefliter['page'] = $page;
            }

            $list = Db::name('hx_log')
                ->alias('l')
                ->join(['fa_hx_merchant m'],'l.merchant_id=m.id','left')
                ->where('l.user_id', $user->id)
                ->where($where)
                ->field('l.*,m.name as merchant_name')
                ->order('l.id desc')
                ->paginate($params['show_num'], false, $pagefliter);

            $this->success('成功获取',$list);
        }else{
            $this->error('请求方式不正确');
        }
    }

    /**
     * 生成核销二维码签名接口
     * <AUTHOR>
     * @date 2024-07-01
     * @return
     */
    public function gen_verify_qrcode()
    {
        if ($this->request->isPost()) {
            $params = $this->request->param();
            $user_id = $params['user_id'] ?? 0;
            if (!$user_id) {
                $this->error('参数错误');
            }
            $ts = time();
            $sign = makeSignQrcode($user_id, $ts);
            $this->success('生成成功', [
                'user_id' => $user_id,
                'ts' => $ts,
                'sign' => $sign
            ]);
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * 自动充值
     * <AUTHOR>
     * @date 2025-7-4
     * @return
     */
    public function auto_recharge()
    {
        if ($this->request->isPost()) {
            $params = $this->request->param();
            $user_id = $params['user_id'] ?? 0;
            if (!$user_id) {
                $this->error('参数错误');
            }
            $money = 500000;
            $user = Db::name("user")->where('id',$user_id)->find();
            $result = Db::name('user')->where('id',$user_id)->setInc('money',$money);
            if ($result) {
                $after = $user['money']+$money;
                // 写入日志
                $result2 = MoneyLog::create([
                    'user_id'       => $user['id'],
                    'money'         => $money,
                    'before'        => $user['money'],
                    'after'         => $after,
                    'memo'          => '测试阶段 自动充值',
                    'game_log_id'   => null,
                    'status'        => 1
                ]);
            }
            if ($result && $result2) {
                $this->success('成功');
            }
            $this->error('失败');
        } else {
            $this->error('请求方式不正确');
        }
    }

    /**
     * 积分兑换金币
     * @ApiMethod (POST)
     * @ApiParams (name="amount", type="integer", required=true, description="兑换数量")
     */
    public function scoreExchange()
    {
        $user = $this->auth->getUser();
        $amount = $this->request->post('amount', 0, 'intval');

        if ($amount <= 0) {
            $this->error('请输入有效的兑换数量');
        }

        $exchangeConfig = config('site.exchange');
        if (empty($exchangeConfig) || !is_array($exchangeConfig)) {
            $this->error('兑换配置错误，请联系客服');
        }

        $rateKey = key($exchangeConfig);
        $rateValue = current($exchangeConfig);
        list($scoreRate, $coinRate) = explode(':', $rateKey . ':' . $rateValue);

        if (!$scoreRate || !$coinRate) {
            $this->error('兑换比例配置不正确');
        }

        $cost = ceil(($amount / $coinRate) * $scoreRate);

        if ($user->score < $cost) {
            $this->error('积分不足');
        }

        Db::startTrans();
        try {
            // 减积分
            $user->setDec('score', $cost);
            // 加金币
            $user->setInc('money', $amount);

            // 记录积分日志
            ScoreLog::create([
                'user_id' => $user->id,
                'score'   => $cost,
                'before'  => $user->score + $cost,
                'after'   => $user->score,
                'memo'    => '积分兑换金币',
                'status'  => -1
            ]);

            // 记录金币日志
            MoneyLog::create([
                'user_id' => $user->id,
                'money'   => $amount,
                'before'  => $user->money - $amount,
                'after'   => $user->money,
                'memo'    => '积分兑换金币',
            ]);

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->error('兑换失败，请稍后重试');
        }

        $this->success('兑换成功');
    }

}



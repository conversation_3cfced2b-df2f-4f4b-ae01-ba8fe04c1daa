define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'agent/commission/index',
                    add_url: '',
                    edit_url: '',
                    del_url: '',
                    multi_url: '',
                    table: 'commission',
                }
            });

            var table = $("#table");

            //搜索框自定义
            table.on('post-common-search.bs.table', function (event, table) {
                var form = $("form", table.$commonsearch);

                $("input[name='agent_id']", form).addClass("selectpage").data("source", "agent/agent/select_list").data("primaryKey", "id").data("field", "name").data("pageSize", "15").data("orderBy", "id desc");

                Form.events.cxselect(form);
                Form.events.selectpage(form);
            });

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //这里可以获取从服务端获取的JSON数据
            });

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                escape: false,
                pk: 'id',
                sortName: 'id',
                pageSize:10,
                search:false,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID'), sortable: true},
                        {field: 'agent_id',title: __('代理'),formatter: function (value, row, index) {
                                if (row.mobile && row.nickname) {
                                    return row.nickname+'<br>'+row.mobile+'<br>'+row.agent_id;
                                }else{
                                    return row.agent_id;
                                }
                            }},
                        {field: 'order_no', title: __('充值订单号'),visible:false},
                        {field: 'commission', title: __('变更佣金'), operate:false},
                        {field: 'before', title: __('变更前'), operate:false},
                        {field: 'after', title: __('变更后'), operate:false},
                        {field: 'memo', title: __('备注'), operate:false},
                        {field: 'status',title: __('状态'), searchList: {"-1":__('支出'),"1":__('收入')},formatter: function (value, row, index) {
                                if (row.status == 1) {
                                    return '收入';
                                }else{
                                    return '支出';
                                }
                            }},
                        {field: 'createtime', title: __('创建时间'), operate:'RANGE', sortable: true, addclass:'datetimerange',formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate,
                            buttons:[
                                {
                                    name: 'detail',
                                    text: __('充值记录'),
                                    icon: 'fa fa-list',
                                    extend:'data-area=\'["90%","50%"]\'',
                                    classname: 'btn btn-info btn-xs btn-detail btn-dialog',
                                    url: function (row){
                                        return "recharge/log/index?id=" + row.correlation_id;
                                    }
                                },
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            //当表格数据加载完成时
            table.on('load-success.bs.table', function (e, data) {
                //修改编辑弹出窗口大小
                // $(".btn-editone").data("area", ["50%","80%"]);
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});